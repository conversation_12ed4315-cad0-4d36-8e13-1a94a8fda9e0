# Build
FROM node:18.19.0-alpine3.17 as build

RUN apk add --no-cache git

WORKDIR /node-app

COPY package.json /node-app
COPY package-lock.json /node-app

# Configure git, Install all dependencies.
RUN npm ci --unsafe-perm=true

COPY src /node-app/src
COPY tsconfig.json /node-app/tsconfig.json

# Build
RUN npm run build

# Deployment
FROM node:18.19.0-alpine3.17

WORKDIR /node-app

RUN apk add curl

# COPY production dependencies and code
COPY --from=build /node-app /node-app

# Using the default port
EXPOSE 8080

ENV NODE_ENV=production
CMD [ "node", "dist/index.js" ]
