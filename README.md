# chat-assist-Backend
Digital Human project of backend service, using 
- [NodeJS](https://nodejs.org)
- [TypeORM](https://typeorm.io/)
- [Routing-Controller](https://github.com/typestack/routing-controllers)

## Local development
1. Install dependencies: `npm i`
2. Run development: `npm run dev`

## Database migration
1. Generate migration file: `npm run migration:generate src/migration/<Description>`
2. Create a new migration file: `npm run migration:create src/migration/<Description>`

## ngrok
1. ngrok http --url=flowered-bloodlike-vallie.ngrok-free.app 10070