import { getRepository } from 'common-db-node/dist/db';
import { Authorized, Body, Delete, Get, HttpCode, JsonController, OnUndefined, Param, Patch, Post, QueryParams, CurrentUser } from 'routing-controllers';
import { CreateFolderRequest, FolderResponse, GetFoldersQuery, UpdateFolderRequest } from '../component/types';
import { AuthorizedUser } from '../component/http';
import { StatusCodes } from 'http-status-codes';
import { Folder } from '../entity/folder';
import { USERS } from '../component/http';
import { AppError } from '../component/http';
import { IsNull } from 'typeorm';

export function getFolderResponse(folder: Folder): FolderResponse {
  return {
    id: folder.id,
    projectId: folder.projectId,
    name: folder.name,
    parent: folder.parent ? getFolderResponse(folder.parent) : undefined,
    folders: folder.folders?.map(getFolderResponse),
    createTime: folder.createTime,
  };
}

@JsonController()
export class FolderController {
  private readonly folderRepository = getRepository(Folder);

  @Get('/v1/folders')
  @Authorized(USERS)
  public async index(@CurrentUser() currentUser: AuthorizedUser, @QueryParams() query: GetFoldersQuery): Promise<FolderResponse[]> {
    return (await this.folderRepository.find({
      relations: ['parent'],
      where: {
        parent: { id: query.parentId ?? IsNull() },
        projectId: query.projectId,
      },
      order: {
        name: 'ASC',
      },
    })).map(getFolderResponse);
  }

  @Post('/v1/folders')
  @HttpCode(StatusCodes.CREATED)
  @Authorized(USERS)
  public async create(@CurrentUser() currentUser: AuthorizedUser, @Body() request: CreateFolderRequest): Promise<FolderResponse> {
    const parent = request.parentId ? await this.folderRepository.findOneByOrFail({ id: request.parentId }) : undefined;
    const folder = await this.folderRepository.save(this.folderRepository.create({
      parent: parent,
      name: request.name,
      projectId: request.projectId,
    }));
    return getFolderResponse(folder);
  }

  @Patch('/v1/folders/:folderId')
  @Authorized(USERS)
  public async update(@CurrentUser() currentUser: AuthorizedUser, @Param('folderId') folderId: string, @Body() request: UpdateFolderRequest): Promise<FolderResponse> {
    let folder = await this.folderRepository.findOneByOrFail({
      id: folderId,
    });
    const parent = request.parentId ? await this.folderRepository.findOneByOrFail({ id: request.parentId }) : undefined;
    folder = await this.folderRepository.save(this.folderRepository.merge(folder, {
      parent: parent,
      name: request.name,
    }));
    return getFolderResponse(folder);
  }

  @Delete('/v1/folders/:folderId')
  @OnUndefined(StatusCodes.NO_CONTENT)
  @Authorized(USERS)
  public async delete(@CurrentUser() currentUser: AuthorizedUser, @Param('folderId') folderId: string): Promise<void> {
    const folder = await this.folderRepository.findOneOrFail({
      relations: ['folders', 'files'],
      where: {
        id: folderId,
      },
    });
    if (folder.folders?.length || folder.files?.length) {
      throw new AppError('folder_not_empty');
    }
    await this.folderRepository.remove(folder);
  }
}
