import { Get, <PERSON>son<PERSON>ontroller, CurrentUser, QueryParams, Post, Authorized, HttpCode, Body, Param, Patch, Delete, OnUndefined  } from 'routing-controllers';
import { AddProjectRequest, FileParseStatus, GetKnowledgeQuery, KnowledgeType, Page, ProjectResponse, ResponseCode, UpdateProjectRequest } from '../component/types';
import { getRepository } from 'common-db-node/dist/db';
import { isPagination, paginate } from '../component/util';
import { StatusCodes } from 'http-status-codes';
import { AuthorizedUser, badRequest } from '../component/http';
import { User } from '../entity/user';
import { Project } from '../entity/project';
import { ILike } from 'typeorm';
import * as uuid from 'uuid';
import { uploadToAliyunOSS } from '../component/service/aliyunOSS';
import { addKnowledgeDocs, createKnowledge, deleteKnowledge, parseKnowledgeDocs } from '../component/service/knowledge';
import { crawlWebsite } from '../component/service/crawlWebsite';
import fs from 'node:fs';
import { File } from '../entity/file';
import { fetchMetadata, Metadata } from '../component/service/fetchMetadata';

function getResponse(entry: Project): ProjectResponse {
  return {
    id: entry.id,
    name: entry.name,
    website: entry.website,
    knowledgeId: entry.knowledgeId,
    settings: entry.settings,
    createTime: entry.createTime.toISOString(),
  };
}

@JsonController()
export class ProjectController {
    private readonly projectRepository = getRepository(Project);
    private readonly userRepository = getRepository(User);
    private readonly fileRepository = getRepository(File);

    @Get(`/v1/projects`)
    @Authorized()
    public async index(@CurrentUser() currentUser: AuthorizedUser, @QueryParams() query: GetKnowledgeQuery): Promise<ProjectResponse[] | Page<ProjectResponse>> {
      const [projects, total] = await this.projectRepository.findAndCount({
        where: {
          user: { id: currentUser.id },
          ...(query.name ? { name: ILike(`%${ query.name }%`) } : {}),
        },
        ...isPagination(query) ? paginate(query.pageNumber, query.pageSize) : undefined,
        order: {
          createTime: 'DESC',
        },
      });
      const items = projects.map(getResponse);
      return isPagination(query) ? { items, total } : items;
    }

    @Post(`/v1/projects`)
    @Authorized()
    @HttpCode(StatusCodes.CREATED)
    public async add(@CurrentUser() currentUser: AuthorizedUser, @Body() request: AddProjectRequest): Promise<ProjectResponse> {
      const user = await this.userRepository.findOneByOrFail({ id: currentUser.id });

      const response = await createKnowledge(request.name, request.name);
      switch (response.code) {
        case 0:
          break;
        case 103:
          throw badRequest(ResponseCode.knowledge_exist);
        default:
          throw badRequest(ResponseCode.create_knowledge_failed);
      }

      if (request.settings?.logo && !request.settings?.logo.startsWith('http') && !request.settings?.logo.startsWith('https')) {
        const matches = request.settings?.logo.match(/^data:(image\/\w+);base64,/);
        if (!matches) {
          throw new Error('Invalid base64 image data');
        }

        const mimeType = matches[1];
        const ext = mimeType.split('/')[1];
        const base64Data = request.settings?.logo.replace(/^data:image\/\w+;base64,/, '');

        const inputImageName = `${uuid.v4()}.${ext}`;
        const objectName = `${request.projectId}/${currentUser.id}/${inputImageName}`;

        request.settings.logo = await uploadToAliyunOSS(objectName, base64Data, false);
      }

      // create new project
      const project = await this.projectRepository.save(this.projectRepository.create({
        id: request.projectId,
        user: user,
        name: request.name,
        knowledgeId: response.data.id,
        website: request.website,
        settings: request.settings,
      }));

      // craw website
      (async () => {
        try {
          const pageFiles = await crawlWebsite(request.website);

          for (const pageFile of pageFiles) {
            try {
              const uploadResponse = await addKnowledgeDocs(
                project.knowledgeId,
                pageFile.filePath,
                pageFile.fileName,
                'text/plain'
              );

              if (!uploadResponse || !uploadResponse.data || !uploadResponse.data[0]) {
                throw new Error('Upload response is invalid: ' + JSON.stringify(uploadResponse));
              }

              const fileId = uploadResponse.data[0].id;
              await this.fileRepository.save(this.fileRepository.create({
                id: fileId,
                name: pageFile.pageTitle || pageFile.pageUrl,
                projectId: request.projectId,
                type: KnowledgeType.Website,
                path: pageFile.pageUrl,
                status: FileParseStatus.Parsed,
              }));
              // parse knowledge docs
              parseKnowledgeDocs(project.knowledgeId, fileId).catch(err => console.error('解析失败', err));

            } catch (error) {
              console.error(`Failed to upload file to knowledge base: ${pageFile.filePath}:`, error);
            } finally {
              await fs.promises.unlink(pageFile.filePath).catch(() => {
                console.warn(`Failed to delete temp file: ${pageFile.filePath}`);
              });
            }
          }
        } catch (err) {
          console.error(`Website crawl failed: ${request.website}`, err);
        }
      })().catch(err => {
        console.error('Unexpected error in IIFE:', err);
      });
        
      return getResponse(project);
    }

    @Patch(`/v1/projects/:projectId`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async update(@CurrentUser() currentUser: AuthorizedUser, @Body() request: UpdateProjectRequest,
      @Param('projectId') projectId: string): Promise<ProjectResponse> {
      const project = await this.projectRepository.findOneByOrFail({ id: projectId });

      // 基础字段
      if (request.name !== undefined && request.name !== project.name) {
        project.name = request.name;
      }

      if (request.settings) {
        if (request.settings.name !== undefined && request.settings.name !== project.settings.name) {
          project.settings.name = request.settings.name;
        }
        if (request.settings.color !== undefined && request.settings.color !== project.settings.color) {
          project.settings.color = request.settings.color;
        }
        if (request.settings.suggestedEnable !== undefined && request.settings.suggestedEnable !== project.settings.suggestedEnable) {
          project.settings.suggestedEnable = request.settings.suggestedEnable;
        }
        if (request.settings.welcomeMsg !== undefined && request.settings.welcomeMsg !== project.settings.welcomeMsg) {
          project.settings.welcomeMsg = request.settings.welcomeMsg;
        }
        if (request.settings.suggestedQuestions !== undefined &&
        JSON.stringify(request.settings.suggestedQuestions) !== JSON.stringify(project.settings.suggestedQuestions)) {
          project.settings.suggestedQuestions = request.settings.suggestedQuestions;
        }
        if (request.settings.whatsappEnable !== undefined && request.settings.whatsappEnable !== project.settings.whatsappEnable) {
          project.settings.whatsappEnable = request.settings.whatsappEnable;
        }
        if (request.settings.whatsappAddress !== undefined && request.settings.whatsappAddress !== project.settings.whatsappAddress) {
          project.settings.whatsappAddress = request.settings.whatsappAddress;
        }
        if (request.settings.formEnable !== undefined && request.settings.formEnable !== project.settings.formEnable) {
          project.settings.formEnable = request.settings.formEnable;
        }
        if (request.settings.currentForm !== undefined && request.settings.currentForm !== project.settings.currentForm) {
          project.settings.currentForm = request.settings.currentForm;
        }
        if (request.settings.externalFormUrl !== undefined && request.settings.externalFormUrl !== project.settings.externalFormUrl) {
          project.settings.externalFormUrl = request.settings.externalFormUrl;
        }
        if (request.settings.internalFormUrl !== undefined && request.settings.internalFormUrl !== project.settings.internalFormUrl) {
          project.settings.internalFormUrl = request.settings.internalFormUrl;
        }
        if (request.settings.notificationEnabled !== undefined && request.settings.notificationEnabled !== project.settings.notificationEnabled) {
          project.settings.notificationEnabled = request.settings.notificationEnabled;
        }
        if (request.settings.notificationEmails !== undefined && request.settings.notificationEmails !== project.settings.notificationEmails) {
          project.settings.notificationEmails = request.settings.notificationEmails;
        }
        if (request.settings.customPrompt !== undefined && request.settings.customPrompt !== project.settings.customPrompt) {
          project.settings.customPrompt = request.settings.customPrompt;
        }
        if (request.settings.logo !== undefined && request.settings.logo !== project.settings.logo) {
          const matches = request.settings?.logo.match(/^data:(image\/\w+);base64,/);
          if (!matches) {
            throw new Error('Invalid base64 image data');
          }

          const mimeType = matches[1];
          const ext = mimeType.split('/')[1];
          const base64Data = request.settings?.logo.replace(/^data:image\/\w+;base64,/, '');

          const inputImageName = `${uuid.v4()}.${ext}`;
          const objectName = `${project.id}/${currentUser.id}/${inputImageName}`;

          const logoUrl = await uploadToAliyunOSS(objectName, base64Data, false);
          project.settings.logo = logoUrl?? '';
        }
      }

      return getResponse(await this.projectRepository.save(project));
    }

    @Get(`/v1/projects/:projectId`)
    @HttpCode(StatusCodes.OK)
    public async getById(@Param('projectId') projectId: string): Promise<ProjectResponse> {
      const project = await this.projectRepository.findOneByOrFail({
        id: projectId,
      });
      return getResponse(project);
    }

    @Get(`/v1/metadata/:website`)
    @HttpCode(StatusCodes.OK)
    public async getMetadata(@Param('website') website: string): Promise<Metadata> {
      return await fetchMetadata(website);
    }

    @Delete(`/v1/projects/:projectId`)
    @OnUndefined(StatusCodes.NO_CONTENT)
    @Authorized()
    public async delete(@Param('projectId') projectId: string): Promise<void> {
      const project = await this.projectRepository.findOneByOrFail({
        id: projectId,
      });

      // delete knowledge
      const errorCode = await deleteKnowledge(project.knowledgeId);
      if (errorCode !== 0) {
        throw badRequest(ResponseCode.delete_knowledge_file_failed);
      }

      // soft delete project
      project.deleteTime = new Date();
      await this.projectRepository.save(project);
    }
}
