import { Authorized, Body, CurrentUser, Delete, Get, HttpCode, JsonController, OnUndefined, Param, Patch, Post, QueryParams, Res } from 'routing-controllers';
import { AddEmailRequest, EmailResponse, GetPagingRequest, Page, UpdateEmailRequest } from '../component/types';
import { getRepository } from 'common-db-node/dist/db';
import { StatusCodes } from 'http-status-codes';
import { AuthorizedUser } from '../component/http';
import { User } from '../entity/user';
import { isPagination } from '../component/util';
import { Mail } from '../entity/email';
import { createGmailService, getGmailAuthUrl } from '../component/service/gMail';

function getResponse(entry: Mail): EmailResponse {
  return {
    id: entry.id,
    projectId: entry.projectId,
    type: entry.type,
    sender: entry.sender,
    email: entry.email,
    authorized: entry.authorized,
    activated: entry.activated,
    companyName: entry.companyName,
    companyAddress: entry.companyAddress,
    productPage: entry.productPage,
    contactUs: entry.contactUs,
    createTime: entry.createTime.toISOString(),
  };
}

@JsonController()
export class EmailController {
    private readonly emailRepository = getRepository(Mail);
    private readonly userRepository = getRepository(User);

    // 临时存储OAuth回调获取的tokens，以邮箱地址为key
    private readonly pendingTokens = new Map<string, {
        accessToken: string;
        refreshToken: string;
        expiresAt: Date;
    }>();

    @Get(`/v1/emails`)
    @Authorized()
    public async index(@QueryParams() query: { projectId?: string; } & GetPagingRequest): Promise<EmailResponse[] | Page<EmailResponse>> {
      const [emails, total] = await this.emailRepository.findAndCount({
        where: {
          projectId: query.projectId,
        },
        order: {
          createTime: 'DESC',
        },
      });
      const items = emails.map(getResponse);
      return isPagination(query) ? { items, total } : items;
    }

    @Get(`/v1/emails/checkAuthorized`)
    @Authorized()
    public async checkAuthorized(@CurrentUser() currentUser: AuthorizedUser, @QueryParams() query: { email: string; projectId?: string; }): Promise<string> {
      console.log('checkAuthorized: ', query.email, query.projectId);

      try {
        const email = await this.emailRepository.findOneBy({ email: query.email });

        if (!email) {
          const stateData = {
            projectId: query.projectId || '',
            userId: currentUser.id,
            email: query.email,
          };
          const state = encodeURIComponent(JSON.stringify(stateData));

          const gmailService = createGmailService();
          return gmailService.getAuthUrl() + `&state=${state}`;
        }

        if (email.accessToken && email.expiresAt > new Date()) {
          return 'true';
        } else {
          const stateData = {
            projectId: query.projectId || email.projectId,
            userId: currentUser.id,
            email: query.email,
          };
          const state = encodeURIComponent(JSON.stringify(stateData));

          const gmailService = createGmailService();
          return gmailService.getAuthUrl() + `&state=${state}`;
        }

      } catch (error) {
        console.warn(`检查邮箱 ${query.email} 授权状态时出错:`, error);
        return getGmailAuthUrl();
      }
    }

    @Post(`/v1/emails`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async add(@CurrentUser() currentUser: AuthorizedUser, @Body() request: AddEmailRequest): Promise<EmailResponse> {
      const user = await this.userRepository.findOneByOrFail({ id: currentUser.id });

      const pendingTokenData = this.pendingTokens.get(request.email);

      let accessToken = '';
      let refreshToken = '';
      let expiresAt = new Date(Date.now() + 60 * 60 * 1000);
      let authorized = false;

      if (pendingTokenData) {
        // 如果找到了tokens，使用它们并从Map中删除
        accessToken = pendingTokenData.accessToken;
        refreshToken = pendingTokenData.refreshToken;
        expiresAt = pendingTokenData.expiresAt;
        authorized = true;

        // 从Map中删除已使用的tokens
        this.pendingTokens.delete(request.email);
        console.log(`✅ 使用临时存储的tokens创建邮箱配置: ${request.email}`);
      } else {
        console.log(`⚠️ 未找到邮箱 ${request.email} 的临时tokens，创建未授权的邮箱配置`);
      }

      const email = await this.emailRepository.save(
        this.emailRepository.create({
          user: user,
          projectId: request.projectId,
          type: request.type,
          sender: request.sender,
          email: request.email,
          authorized: authorized,
          accessToken: accessToken,
          refreshToken: refreshToken,
          expiresAt: expiresAt,
          companyName: request.companyName,
          companyAddress: request.companyAddress,
          productPage: request.productPage,
          contactUs: request.contactUs,
          activated: true,
        })
      );

      return getResponse(email);
    }

    @Patch(`/v1/emails/:mailId`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async update(@Body() request: UpdateEmailRequest, @Param('mailId') mailId: string ): Promise<EmailResponse> {
      const email = await this.emailRepository.findOneByOrFail({ id: mailId });
      if (request.activated !== undefined) { email.activated = request.activated; }
      if (request.companyName !== undefined) { email.companyName = request.companyName; }
      if (request.companyAddress !== undefined) { email.companyAddress = request.companyAddress; }
      if (request.productPage !== undefined) { email.productPage = request.productPage; }
      if (request.contactUs !== undefined) { email.contactUs = request.contactUs; }

      return getResponse(await this.emailRepository.save(email));
    }

    @Delete(`/v1/emails/:mailId`)
    @OnUndefined(StatusCodes.NO_CONTENT)
    @Authorized()
    public async delete(@Param('mailId') mailId: string): Promise<void> {
      const email = await this.emailRepository.findOneByOrFail({
        id: mailId,
      });
      email.deleteTime = new Date();
      await this.emailRepository.save(email);
    }

    @Get(`/v1/auth/gmail/callback`)
    @HttpCode(StatusCodes.OK)
    public async webhook(@QueryParams() query: { code?: string; state?: string; error?: string; email?: string }): Promise<void> {
      console.log('Gmail授权回调:', { code: query.code, state: query.state, error: query.error, email: query.email });

      if (query.error) {
        console.error('Gmail授权被拒绝:', query.error);
        return;
      }

      if (!query.code) {
        console.error('缺少授权码');
        return;
      }

      try {
        const gmailService = createGmailService();
        const tokens = await gmailService.getTokenFromCode(query.code);
        console.log('✅ 成功获取Gmail访问令牌');

        gmailService.setCredentials(tokens.accessToken, tokens.refreshToken);
        const profile = await gmailService.getUserProfile();
        const userEmail = profile.emailAddress;

        if (!userEmail) {
          throw new Error('无法获取用户邮箱地址');
        }

        const expiresAt = new Date(Date.now() + 60 * 60 * 1000);
        this.pendingTokens.set(userEmail, {
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          expiresAt: expiresAt,
        });

        console.log(`✅ Tokens已存储到临时Map中，邮箱: ${userEmail}`);
      } catch (error) {
        console.error('❌ Gmail授权回调处理失败:', error);
      }
    }
}
