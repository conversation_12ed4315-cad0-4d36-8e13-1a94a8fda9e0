import { Body, HttpCode, JsonController, Post, Get, QueryParams, Res, Req } from 'routing-controllers';
import { StatusCodes } from 'http-status-codes';
import { getRepository } from 'common-db-node/dist/db';
import { LoginRequest, ResponseCode, TokenResponse } from '../component/types';
import { asyncFilter, generateToken, verify } from '../component/util';
import { User } from '../entity/user';
import { unauthorized } from '../component/http';
import { Issuer, Client, TokenSet } from 'openid-client';
import { config } from '../component/config';
import { Role } from '../entity/role';
import { Response, Request } from 'express';

@JsonController()
export class AuthController {
  private readonly userRepository = getRepository(User);
  private readonly roleRepository = getRepository(Role);

  @Post(`/v1/token`)
  @HttpCode(StatusCodes.OK)
  public async login(@Body() request: LoginRequest): Promise<TokenResponse | unknown> {
    if (request.email && request.password) {
      const email = request.email;
      const password = request.password;

      let users = await this.userRepository.findBy({ email });

      users = await asyncFilter(users, user => verify(password, user.password));

      if (!users.length) {
        throw unauthorized(ResponseCode.invalid_username_or_password);
      }

      const user = users[0];
      user.lastLoginTime = new Date();
      await this.userRepository.save(user);

      return {
        token: generateToken(user),
      };
    } else {
      throw unauthorized(ResponseCode.invalid_user);
    }
  }

  @Get('/v1/sso/google/callback')
  public async googleLogin(@QueryParams() query: any, @Req()req: Request, @Res() res: Response): Promise<any> {
    console.log('googleLogin: ', query);

    const roles = ['normal_user'];
    if (!query.code || !query.state) {
      throw unauthorized(ResponseCode.invalid_user);
    }

    const client = await AuthController.googleClient;

    let tokenSet: TokenSet;
    try {
      const checks = { state: query.state };

      tokenSet = await client.callback(
        config.SSO.google.login_redirect_uri,
        query,
        checks
      );
    } catch (err) {
      console.error('Google token exchange failed', err);
      throw unauthorized(ResponseCode.invalid_user);
    }

    const claims = tokenSet.claims();
    const externalId = claims.sub;
    if (!externalId || !claims.email) {
      throw unauthorized(ResponseCode.invalid_user);
    }

    let user = await this.userRepository.findOne({
      where: [{ googleSub: externalId }, { email: claims.email }],
    });

    if (!user) {
      user = this.userRepository.create({
        name: claims.name ?? claims.email,
        email: claims.email,
        googleSub: externalId,
        roles: roles.map((role) =>
          this.roleRepository.create({ name: role })),
        password: '',
      });
    } else if (!user.googleSub) {
      user.googleSub = externalId;
    }
    user.lastLoginTime = new Date();
    await this.userRepository.save(user);

    const token = generateToken(user);

    res.writeHead(302, {
      'Location': `${config.settings.redirectUrl}?token=${encodeURIComponent(token)}`,
    });
    res.end();
  }

  private static googleClient: Promise<Client> = (async () => {
    const googleIssuer = new Issuer({
      issuer: 'https://accounts.google.com',
      authorization_endpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
      token_endpoint: 'https://oauth2.googleapis.com/token',
      userinfo_endpoint: 'https://openidconnect.googleapis.com/v1/userinfo',
      jwks_uri: 'https://www.googleapis.com/oauth2/v3/certs',
      response_types_supported: ['code'],
      subject_types_supported: ['public'],
      id_token_signing_alg_values_supported: ['RS256'],
    });

    return new googleIssuer.Client({
      client_id: config.SSO.google.client_id,
      client_secret: config.SSO.google.client_secret,
      redirect_uris: [config.SSO.google.login_redirect_uri],
      response_types: ['code'],
    });
  })();
}
