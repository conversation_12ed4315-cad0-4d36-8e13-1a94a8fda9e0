import { JsonController, Post, Body, Res, OnUndefined, Get, Authorized, QueryParams } from 'routing-controllers';
import { ChatRequest, ChatResponse, GetChatsRequest, Page } from '../component/types';
import { StatusCodes } from 'http-status-codes';
import { Response } from 'express';
import { Chat } from '../entity/chat';
import { getRepository } from 'common-db-node/dist/db';
import { isPagination, paginate } from '../component/util';
import { Session } from '../entity/session';
import { Project } from '../entity/project';
import { chatApiRequest, createAgentSession } from '../component/service/knowledge';
import { Form } from '../entity/form';

function getResponse(entry: Chat): ChatResponse {
  return {
    sessionId: entry.sessionId,
    question: entry.question,
    answer: entry.answer,
    reference: entry.reference,
    like: entry.like,
    createTime: entry.createTime.toISOString(),
  };
}

@JsonController()
export class ChatController {
  private readonly chatRepository = getRepository(Chat);
  private readonly sessionRepository = getRepository(Session);
  private readonly projectRepository = getRepository(Project);
  private readonly formRepository = getRepository(Form);

  @Get(`/v1/chats`)
  @Authorized()
  public async index(@QueryParams() query: GetChatsRequest): Promise<ChatResponse[] | Page<ChatResponse>> {
    const [chats, total] = await this.chatRepository.findAndCount({
      where: {
        sessionId: query.sessionId,
      },
      ...isPagination(query) ? paginate(query.pageNumber, query.pageSize) : undefined,
      order: {
        createTime: 'ASC',
      },
    });

    const items = chats.map(getResponse);
    return isPagination(query) ? { items, total } : items;
  }

  @Post(`/v1/chat`)
  @OnUndefined(StatusCodes.OK)
  public async chat(@Body() request: ChatRequest, @Res() res: Response): Promise<void> {
    let question = request.question;
    let session;

    const project = await this.projectRepository.findOneByOrFail({
      id: request.projectId,
    });

    if (!request.sessionId) {
      const form = await this.formRepository.findOneBy({ projectId: project.id });
      const sessionId = await createAgentSession(project.knowledgeId, project.settings.currentForm === 0 ? form?.url : project.settings.externalFormUrl, project.settings.whatsappAddress);

      session = await this.sessionRepository.save(this.sessionRepository.create({
        id: sessionId,
        projectId: request.projectId,
        name: request.question,
      }));
      request.sessionId = session.id;
    } else {
      session = await this.sessionRepository.findOneByOrFail({
        id: request.sessionId,
      });
      session.name = request.question;
      session.updateTime = new Date();
      await this.sessionRepository.save(session);
    }

    if (session.formFilled) {
      question = question + '<form_filled>';
    }

    res.setHeader('Content-Type', 'text/event-stream;charset=utf-8');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.flushHeaders();

    const chat = await this.chatRepository.save(this.chatRepository.create({
      sessionId: request.sessionId,
      question: request.question,
    }));

    let answer: any = null;
    let reference: any = null;
    return chatApiRequest(question, project.knowledgeId, request.sessionId)
      .then((response) => {
        return new Promise((resolve, reject) => {
          response.data.on('data', (chunk: any) => {
            const data = chunk.toString();
            try {
              const cleanData = data.startsWith('data:') ? data.slice(5) : data;
              const parsedData = JSON.parse(cleanData);
              if (parsedData && parsedData.data) {
                if (parsedData.data.answer) {
                  answer = parsedData.data.answer.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
                }
                if (parsedData.data.reference) {
                  reference = parsedData.data.reference;
                }
              }
            } catch (error) {
              console.error('Error parsing data:', error);
              console.error('Raw data:', chunk.toString());
            }
            res.write(`${data}`);
          });
          response.data.on('end', async () => {
            chat.answer = answer;
            chat.reference = reference;
            await this.chatRepository.save(chat);
            res.end();
          });
        });
      });
  }
}
