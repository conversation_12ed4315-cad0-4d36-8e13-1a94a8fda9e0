import { Authorized, Body, CurrentUser, Delete, Get, JsonController, OnUndefined, Param, Patch, Post, QueryParams, UploadedFiles } from 'routing-controllers';
import { getRepository } from 'common-db-node/dist/db';
import { AuthorizedUser, badRequest } from '../component/http';
import { File } from '../entity/file';
import { AddFileRequest, AddWebsiteRequest, FileParseStatus, FileResponse, GetFilesQuery, FileTypeStatistics, KnowledgeType, Page, ResponseCode, UpdateFileRequest, ChunkMethod } from '../component/types';
import { getFileExtension, isPagination, paginate } from '../component/util';
import { StatusCodes } from 'http-status-codes';
import { getFolderResponse } from './foldercontroller';
import { IsNull } from 'typeorm';
import { Folder } from '../entity/folder';
import * as fs from 'node:fs';
import * as path from 'path';
import { config } from '../component/config';
import axios from 'axios';
import FormData from 'form-data';
import { Project } from '../entity/project';
import { Faq } from '../entity/faq';
import { deleteKnowledgeDocs, parseKnowledgeDocs, addKnowledgeDocs, updateKnowledgeDocs } from '../component/service/knowledge';
import { crawlWebsite } from '../component/service/crawlWebsite';
import { ConvertOptions, convertPdfToMarkdown } from '../component/service/textin';

function getResponse(entry: File): FileResponse {
  return {
    id: entry.id,
    projectId: entry.projectId,
    name: entry.name,
    type: entry.type,
    size: entry.size,
    path: entry.path,
    status: entry.status,
    folder: entry.folder ? getFolderResponse(entry.folder) : undefined,
    mimeType: entry.mimeType,
    createTime: entry.createTime.toISOString(),
  };
}

@JsonController()
export class FileController {
  private readonly fileRepository = getRepository(File);
  private readonly folderRepository = getRepository(Folder);
  private readonly projectRepository = getRepository(Project);
  private readonly faqRepository = getRepository(Faq);

  @Get(`/v1/files`)
  @Authorized()
  public async index(@CurrentUser() currentUser: AuthorizedUser, @QueryParams() query: GetFilesQuery): Promise<FileResponse[] | Page<FileResponse>> {
    const where: any = {
      folder: { id: query.folderId ?? IsNull() },
      projectId: query.projectId,
    };
    if(query.type) where.type = query.type;
    const [files, total] = await this.fileRepository.findAndCount({
      relations: ['folder'],
      where,
      ...isPagination(query) ? paginate(query.pageNumber, query.pageSize) : undefined,
      order: {
        createTime: 'DESC',
      },
    });
    const items = files.map(getResponse);
    return isPagination(query) ? { items, total } : items;
  }

  @Get(`/v1/files/statistics`)
  @Authorized()
  public async getFileStatisticsByType( @QueryParams() { projectId }: { projectId: string }): Promise<FileTypeStatistics[]> {
    // 使用 TypeORM 的查询构建器进行分组统计
    const statisticsQuery = this.fileRepository
      .createQueryBuilder('file')
      .select('file.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('file.type');

    // 如果指定了项目ID，添加过滤条件
    if (projectId) {
      statisticsQuery.where('file.projectId = :projectId', { projectId });
    }

    const rawResults = await statisticsQuery.getRawMany();

    // 转换结果格式
    const statistics: FileTypeStatistics[] = rawResults.map(result => ({
      type: result.type as KnowledgeType,
      count: parseInt(result.count, 10),
    }));

    // 确保所有类型都有统计结果，即使数量为0
    const allTypes = Object.values(KnowledgeType);
    const completeStatistics: FileTypeStatistics[] = allTypes.map(type => {
      const existingStat = statistics.find(stat => stat.type === type);
      return existingStat || { type, count: 0 };
    });

    const faqCount = await this.faqRepository.count({
      where: {
        projectId,
      },
    });
    const faq = completeStatistics.find(s => s.type == KnowledgeType.FAQ);
    faq!.count = faqCount;

    return completeStatistics;
  }

  @Post(`/v1/addWebsite`)
  @Authorized()
  @OnUndefined(StatusCodes.CREATED)
  public async addWebsite(@Body() request: AddWebsiteRequest): Promise<FileResponse[]> {
    const project = await this.projectRepository.findOneByOrFail({
      id: request.projectId,
    });

    const results: FileResponse[] = [];
    try {
      const pageFiles = await crawlWebsite(request.websiteUrl);

      for (const pageFile of pageFiles) {
        try {
          const uploadResponse = await addKnowledgeDocs(
            project.knowledgeId,
            pageFile.filePath,
            pageFile.fileName,
            'text/plain'
          );

          if (!uploadResponse || !uploadResponse.data || !uploadResponse.data[0]) {
            throw new Error('Upload response is invalid: ' + JSON.stringify(uploadResponse));
          }

          const fileId = uploadResponse.data[0].id;
          const savedFile = await this.fileRepository.save(
            this.fileRepository.create({
              id: fileId,
              name: pageFile.pageTitle || pageFile.pageUrl,
              projectId: request.projectId,
              type: KnowledgeType.Website,
              path: pageFile.pageUrl,
              status: FileParseStatus.Parsed,
            })
          );

          results.push({
            id: savedFile.id,
            name: savedFile.name,
            projectId: savedFile.projectId,
            type: savedFile.type,
            path: savedFile.path,
            status: savedFile.status,
            mimeType: savedFile.mimeType,
            size: savedFile.size,
            createTime: savedFile.createTime.toISOString(),
          });

          parseKnowledgeDocs(project.knowledgeId, fileId).catch((err) =>
            console.error('解析失败', err)
          );

        } catch (error) {
          console.error(`Failed to upload file to knowledge base: ${pageFile.filePath}:`, error);
        } finally {
          await fs.promises.unlink(pageFile.filePath).catch(() => {
            console.warn(`Failed to delete temp file: ${pageFile.filePath}`);
          });
        }
      }
    } catch (err) {
      console.error(`Website crawl failed: ${request.websiteUrl}`, err);
    }

    return results;
  }

  @Post(`/v1/addFiles`)
  @Authorized()
  @OnUndefined(StatusCodes.CREATED)
  public async addFiles(@CurrentUser() currentUser: AuthorizedUser, @Body() request: AddFileRequest,
      @UploadedFiles('files', { options: { dest: '/tmp' } }) uploadedFiles: Express.Multer.File[]): Promise<FileResponse[]> {
    const processedFiles: string[] = [];
    const results: FileResponse[] = [];

    const project = await this.projectRepository.findOneByOrFail({
      id: request.projectId,
    });

    try {
      const folder = request.folderId ? await this.folderRepository.findOneBy({
        id: request.folderId,
        projectId: request.projectId,
      }) : null;

      for (const uploadedFile of uploadedFiles) {
        const fileExtension = getFileExtension(uploadedFile.originalname);
        const fileName = uploadedFile.filename + fileExtension;
        const destPath = path.join(config.cache.docPath, fileName);

        // 复制文件到目标目录
        await fs.promises.copyFile(uploadedFile.path, destPath);
        console.log(`File copied successfully to ${destPath}`);
        processedFiles.push(destPath);

        // 处理文件名编码
        let originalFileName = '';
        try {
          originalFileName = Buffer.from(uploadedFile.originalname, 'latin1').toString('utf-8');
        } catch {
          originalFileName = decodeURIComponent(uploadedFile.originalname);
        }

        // 上传知识文档
        try {
          let fileId = '';
          if (fileExtension.toLowerCase() === '.pdf') {
            const mdFileName = originalFileName + '.txt';
            const mdFilePath = path.join(config.cache.docPath, mdFileName);
            // await this.convertPdfToMarkdown(destPath, mdFileName, mdFilePath);

            const params : ConvertOptions = {
              inputPath: destPath,
              outputPath: mdFilePath,
            };
            await convertPdfToMarkdown(params);

            try {
              const uploadResponse = await addKnowledgeDocs(project.knowledgeId, mdFilePath, mdFileName, 'text/plain');
              if (!uploadResponse || !uploadResponse.data || !uploadResponse.data[0]) {
                throw new Error('Upload response is invalid: ' + JSON.stringify(uploadResponse));
              }
              fileId = uploadResponse.data[0].id;
            } catch (e) {
              console.error('Error uploading markdown file:', e);
              // 删除生成的 markdown 文件
              await fs.promises.unlink(mdFilePath).catch(() => { /* 忽略错误 */ });
              throw e;
            }
          } else {
            const uploadResponse = await addKnowledgeDocs(project.knowledgeId, destPath, originalFileName, fileExtension);
            if (!uploadResponse || !uploadResponse.data || !uploadResponse.data[0]) {
              throw new Error('Upload response is invalid: ' + JSON.stringify(uploadResponse));
            }
            fileId = uploadResponse.data[0].id;
          }

          const savedFile = await this.fileRepository.save(this.fileRepository.create({
            id: fileId,
            name: originalFileName,
            projectId: request.projectId,
            type: KnowledgeType.Document,
            mimeType: fileExtension,
            size: uploadedFile.size,
            folder: folder ?? undefined,
            path: destPath,
            status: FileParseStatus.Parsed,
          }));

          results.push({
            id: savedFile.id,
            name: savedFile.name,
            projectId: savedFile.projectId,
            type: savedFile.type,
            path: savedFile.path,
            status: savedFile.status,
            mimeType: savedFile.mimeType,
            size: savedFile.size,
            createTime: savedFile.createTime.toISOString(),
          });

          parseKnowledgeDocs(project.knowledgeId, fileId).catch(err => console.error('解析失败', err));

        } catch (e) {
          console.log('Error upload knowledge docs:', request.projectId, destPath, e);
          await fs.promises.unlink(destPath);
          throw badRequest(ResponseCode.upload_knowledge_failed);
        }
      }
    } finally {
      // 删除临时目录中的原始上传文件
      for (const uploadedFile of uploadedFiles) {
        await fs.promises.unlink(uploadedFile.path).catch(() => { /* 忽略错误 */ });
      }

      // 如果出错，删除已复制到目标路径的文件
      if (processedFiles.length > 0) {
        for (const filePath of processedFiles) {
          await fs.promises.unlink(filePath).catch(() => { /* 忽略错误 */ });
        }
      }
    }
    return results;
  }

  @Patch('/v1/files/:fileId')
  @Authorized()
  public async update(@Param('fileId') fileId: string, @Body() request: UpdateFileRequest): Promise<FileResponse> {
    let file = await this.fileRepository.findOneByOrFail({
      id: fileId,
    });
    file = await this.fileRepository.save(this.fileRepository.merge(file, {
      name: request.name,
    }));
    return getResponse(file);
  }

  @Delete(`/v1/files/:fileId`)
  @OnUndefined(StatusCodes.NO_CONTENT)
  @Authorized()
  public async delete(@Param('fileId') fileId: string): Promise<void> {
    console.log('delete file: ', fileId);
    const file = await this.fileRepository.findOneByOrFail({
      id: fileId,
    });

    const project = await this.projectRepository.findOneByOrFail({
      id: file.projectId,
    });
    
    const errorCode = await deleteKnowledgeDocs(project.knowledgeId, fileId);
    if (errorCode !== 0) {
      throw badRequest(ResponseCode.delete_knowledge_failed);
    }
    await this.fileRepository.remove(file);
  }

  private async convertPdfToMarkdown(filePath: string, fileName: string, mdFilePath: string): Promise<void> {
    const formData = new FormData();
    formData.append('pdf_file', fs.createReadStream(filePath), {
      filename: fileName,
      contentType: 'application/pdf',
    });

    const response = await axios.post(config.knowledgeBase.minerU_url, formData, {
      headers: {
        'Content-Type': `multipart/form-data; boundary=${formData.getBoundary()}`,
      },
      maxBodyLength: Infinity,
      maxContentLength: Infinity,
      maxRedirects: 0,
    });
    console.log('parse knowledge docs with minerU: ', filePath, response.data);

    // 使用 promises API 替代回调式 API，确保文件写入完成
    await fs.promises.writeFile(mdFilePath, response.data.content, 'utf8');
    console.log(`Markdown 文件已保存到: ${mdFilePath}`);
  }
}
