import { Authorized, Body, CurrentUser, Delete, Get, HttpCode, JsonController, OnUndefined, Param, Patch, Post, QueryParams } from 'routing-controllers';
import { AddFormRequest, FormResponse, GetPagingRequest, Page, TemplateResponse, UpdateFormRequest } from '../component/types';
import { getRepository } from 'common-db-node/dist/db';
import { StatusCodes } from 'http-status-codes';
import { Form } from '../entity/form';
import { FormTemplate } from '../entity/formTemplate';
import { AuthorizedUser } from '../component/http';
import { User } from '../entity/user';
import { uploadToAliyunOSS } from '../component/service/aliyunOSS';
import * as uuid from 'uuid';
import { config } from '../component/config';
import { isPagination } from '../component/util';
import { Project } from '../entity/project';

function getFormResponse(entry: Form): FormResponse {
  return {
    id: entry.id,
    projectId: entry.projectId,
    name: entry.name,
    description: entry.description,
    logo: entry.logo,
    content: entry.content,
    url: entry.url,
    active: entry.active,
    createTime: entry.createTime.toISOString(),
    updateTime: entry.updateTime.toISOString(),
  };
}

function getTemplateResponse(entry: FormTemplate): TemplateResponse {
  return {
    id: entry.id,
    name: entry.name,
    description: entry.description,
    logo: entry.logo,
    content: entry.content,
    createTime: entry.createTime.toISOString(),
  };
}

@JsonController()
export class FormController {
    private readonly formRepository = getRepository(Form);
    private readonly formTemplateRepository = getRepository(FormTemplate);
    private readonly userRepository = getRepository(User);
    private readonly projectRepository = getRepository(Project);

    @Get(`/v1/forms`)
    @Authorized()
    public async getForms(
      @QueryParams()
        query: {
        projectId?: string;
      } & GetPagingRequest
    ): Promise<FormResponse[] | Page<FormResponse>> {
      const [forms, total] = await this.formRepository.findAndCount({
        where: {
          projectId: query.projectId,
        },
        order: {
          createTime: 'DESC',
        },
      });
      const items = forms.map(getFormResponse);
      return isPagination(query) ? { items, total } : items;
    }

    @Get(`/v1/forms/:formId`)
    @Authorized()
    public async getForm(@Param('formId') formId: string): Promise<FormResponse> {
      const product = await this.formRepository.findOneByOrFail({
        id: formId,
      });
      return getFormResponse(product);
    }

    @Get(`/v1/formTemplates`)
    @Authorized()
    public async getTemplates(@QueryParams() query: GetPagingRequest): Promise<TemplateResponse[] | Page<TemplateResponse>> {
      const [templates, total] = await this.formTemplateRepository.findAndCount({
        order: {
          createTime: 'ASC',
        },
      });
      const items = templates.map(getTemplateResponse);
      return isPagination(query) ? { items, total } : items;
    }

    @Post(`/v1/forms`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async add(@CurrentUser() currentUser: AuthorizedUser, @Body() request: AddFormRequest): Promise<FormResponse> {
      const user = await this.userRepository.findOneByOrFail({ id: currentUser.id });

      let fileUrl;
      if (request.logo && request.logo !== '') {
        if (request.logo.startsWith('http') || request.logo.startsWith('https')) {
          fileUrl = request.logo;
        } else {
          const matches = request.logo.match(/^data:(image\/\w+);base64,/);
          if (!matches) {
            throw new Error('Invalid base64 image data');
          }

          const mimeType = matches[1];
          const ext = mimeType.split('/')[1];
          const base64Data = request.logo.replace(/^data:image\/\w+;base64,/, '');

          const inputImageName = `${uuid.v4()}.${ext}`;
          const objectName = `${request.projectId}/${currentUser.id}/${inputImageName}`;

          fileUrl = await uploadToAliyunOSS(objectName, base64Data, true);
        }
      }

      const form = await this.formRepository.save(
        this.formRepository.create({
          user: user,
          projectId: request.projectId,
          name: request.name,
          description: request.description,
          logo: fileUrl,
          content: request.content,
          active: request.active,
        })
      );

      form.url = String(config.settings.prefix) + form.id;
      await this.formRepository.save(form);

      if (form.active) {
        const project = await this.projectRepository.findOneByOrFail({ id: request.projectId });
        project.settings.internalFormUrl = form.url;
        await this.projectRepository.save(project);
      }

      return getFormResponse(form);
    }

    @Patch(`/v1/forms/:formId`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async update(@CurrentUser() currentUser: AuthorizedUser,
      @Body() request: UpdateFormRequest, @Param('formId') formId: string ): Promise<FormResponse> {
      const form = await this.formRepository.findOneByOrFail({ id: formId });
      if (request.name !== undefined) { form.name = request.name; }
      if (request.description !== undefined) { form.description = request.description; }
      if (request.content !== undefined) { form.content = request.content; }
      if (request.active !== undefined) { 
        form.active = request.active;
        if (form.active) {
          const project = await this.projectRepository.findOneByOrFail({ id: form.projectId });
          project.settings.internalFormUrl = form.url;
          await this.projectRepository.save(project);
        }
      }

      if (request.logo) {
        const matches = request.logo.match(/^data:(image\/\w+);base64,/);
        if (!matches) {
          throw new Error('Invalid base64 image data');
        }

        const mimeType = matches[1];
        const ext = mimeType.split('/')[1];
        const base64Data = request.logo.replace(/^data:image\/\w+;base64,/, '');

        const inputImageName = `${uuid.v4()}.${ext}`;
        const objectName = `${form.projectId}/${currentUser.id}/${inputImageName}`;

        const logoUrl = await uploadToAliyunOSS(objectName, base64Data, false);
        form.logo = logoUrl?? '';
      }

      return getFormResponse(await this.formRepository.save(form));
    }

    @Delete(`/v1/forms/:formId`)
    @OnUndefined(StatusCodes.NO_CONTENT)
    @Authorized()
    public async delete(@Param('formId') formId: string): Promise<void> {
      const product = await this.formRepository.findOneByOrFail({
        id: formId,
      });
      product.deleteTime = new Date();
      await this.formRepository.save(product);
    }

    @Patch(`/v1/forms/:formId/active`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async setActive(@Param('formId') formId: string): Promise<FormResponse> {
      const form = await this.formRepository.findOneByOrFail({ id: formId });
      
      // 先将同一项目下的其他已激活表单设置为非激活状态
      await this.formRepository.update(
        { projectId: form.projectId, active: true },
        { active: false }
      );
      
      // 激活当前表单
      form.active = true;
      const project = await this.projectRepository.findOneByOrFail({ id: form.projectId });
      project.settings.internalFormUrl = form.url;
      await this.projectRepository.save(project);
      return getFormResponse(await this.formRepository.save(form));
    }
}
