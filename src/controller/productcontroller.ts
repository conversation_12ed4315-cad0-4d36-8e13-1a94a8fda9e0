import { Authorized, Delete, Get, JsonController, OnUndefined, Param } from 'routing-controllers';
import {  ProductResponse } from '../component/types';
import { getRepository } from 'common-db-node/dist/db';
import { StatusCodes } from 'http-status-codes';
import { Product } from '../entity/product';

function getResponse(entry: Product): ProductResponse {
  return {
    id: entry.id,
    name: entry.name,
    type: entry.type,
    description: entry.description,
    responses: entry.responses,
    bots: entry.bots,
    teamMembers: entry.teamMembers,
    branding: entry.branding,
    documents: entry.documents,
    popular: entry.popular,
    price: entry.price,
    interval: entry.interval,
    paymentLink: entry.paymentLink,
    additionalInfo: entry.additionalInfo,
    createTime: entry.createTime.toISOString(),
  };
}

@JsonController()
export class ProductController {
    private readonly productRepository = getRepository(Product);

    @Get(`/v1/products`)
    public async index(): Promise<ProductResponse[]> {
      const products = await this.productRepository.find({
        order: {
          createTime: 'ASC',
        },
      });
      return products.map(getResponse);
    }

    @Get(`/v1/products/:productId`)
    @Authorized()
    public async get(@Param('productId') productId: string): Promise<ProductResponse> {
      const product = await this.productRepository.findOneByOrFail({
        id: productId,
      });
      return getResponse(product);
    }

    @Delete(`/v1/products/:productId`)
    @OnUndefined(StatusCodes.NO_CONTENT)
    @Authorized()
    public async delete(@Param('productId') productId: string): Promise<void> {
      const product = await this.productRepository.findOneByOrFail({
        id: productId,
      });
      product.deleteTime = new Date();
      await this.productRepository.save(product);
    }
}
