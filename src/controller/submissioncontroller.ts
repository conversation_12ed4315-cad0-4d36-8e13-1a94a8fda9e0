import { Authorized, Body, Get, HttpCode, JsonController, Post, QueryParams } from 'routing-controllers';
import { AddSubmissionRequest, Page, SubmissionResponse } from '../component/types';
import { getRepository } from 'common-db-node/dist/db';
import { StatusCodes } from 'http-status-codes';
import { Submission } from '../entity/submission';
import { isPagination, paginate } from '../component/util';

function getResponse(entry: Submission): SubmissionResponse {
  return {
    id: entry.id,
    formId: entry.formId,
    content: entry.content,
    createTime: entry.createTime.toISOString(),
  };
}

@JsonController()
export class SubmissionController {
  private readonly submissionRepository = getRepository(Submission);

  @Get(`/v1/submissions`)
  @Authorized()
  public async index(
    @QueryParams() query: { formId: string; pageNumber?: string; pageSize?: string }
  ): Promise<SubmissionResponse[] | Page<SubmissionResponse>> {
    const [submissions, total] = await this.submissionRepository.findAndCount({
      where: {
        formId: query.formId,
      },
      ...isPagination(query) ? paginate(query.pageNumber, query.pageSize) : undefined,
      order: {
        createTime: 'ASC',
      },
    });

    const items = submissions.map(getResponse);
    return isPagination(query) ? { items, total } : items;
  }

  @Post(`/v1/submissions`)
  @HttpCode(StatusCodes.OK)
  public async add(
    @Body() request: AddSubmissionRequest
  ): Promise<SubmissionResponse> {
    const form = await this.submissionRepository.save(
      this.submissionRepository.create({
        formId: request.formId,
        content: request.content,
      })
    );
    return getResponse(form);
  }
}