import { Authorized, Body, CurrentUser, Delete, Get, Http<PERSON>ode, JsonController, OnUndefined, Param, Patch, Post } from 'routing-controllers';
import { AddPipelineRequest, PipelineResponse, PipelineTemplateResponse, UpdatePipelineRequest } from '../component/types';
import { getRepository } from 'common-db-node/dist/db';
import { StatusCodes } from 'http-status-codes';
import { Pipeline } from '../entity/pipeline';
import { User } from '../entity/user';
import { AuthorizedUser } from '../component/http';
import { PipelineTemplate } from '../entity/pipelineTemplate';

function getResponse(entry: Pipeline): PipelineResponse {
  return {
    id: entry.id,
    projectId: entry.projectId,
    pipeline: entry.pipeline,
    createTime: entry.createTime.toISOString(),
    updateTime: entry.updateTime.toISOString(),
  };
}

function getTemplateResponse(entry: PipelineTemplate): PipelineTemplateResponse {
  return {
    id: entry.id,
    name: entry.name,
    description: entry.description,
    content: entry.content,
    createTime: entry.createTime.toISOString(),
  };
}

@JsonController()
export class PipelineController {
    private readonly pipelineRepository = getRepository(Pipeline);
    private readonly pipelineTemplateRepository = getRepository(PipelineTemplate);
    private readonly userRepository = getRepository(User);

    @Get(`/v1/pipelines`)
    @Authorized()
    public async index(): Promise<PipelineResponse[]> {
      const pipelines = await this.pipelineRepository.find({
        order: {
          createTime: 'ASC',
        },
      });
      return pipelines.map(getResponse);
    }

    @Get(`/v1/pipelineTemplates`)
    @Authorized()
    public async getTemplates(): Promise<PipelineTemplateResponse[]> {
      const pipelines = await this.pipelineTemplateRepository.find({
        order: {
          createTime: 'ASC',
        },
      });
      return pipelines.map(getTemplateResponse);
    }

    @Post(`/v1/pipelines`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async add(@CurrentUser() currentUser: AuthorizedUser, @Body() request: AddPipelineRequest): Promise<PipelineResponse> {
      const user = await this.userRepository.findOneByOrFail({ id: currentUser.id });
      const pipeline = await this.pipelineRepository.save(this.pipelineRepository.create({
        user: user,
        projectId: request.projectId,
        pipeline: request.pipeline,
      }));
      return getResponse(pipeline);
    }

    @Patch(`/v1/pipelines/:pipelineId`)
    @Authorized()
    @HttpCode(StatusCodes.OK)
    public async update(@Body() request: UpdatePipelineRequest, @Param('pipelineId') pipelineId: string): Promise<PipelineResponse> {
      const pipeline = await this.pipelineRepository.findOneByOrFail({ id: pipelineId });

      if (request.pipeline !== undefined) { pipeline.pipeline = request.pipeline; }

      return getResponse(await this.pipelineRepository.save(pipeline));
    }

    @Delete(`/v1/pipelines/:pipelineId`)
    @OnUndefined(StatusCodes.NO_CONTENT)
    @Authorized()
    public async delete(@Param('pipelineId') pipelineId: string): Promise<void> {
      const pipeline = await this.pipelineRepository.findOneByOrFail({
        id: pipelineId,
      });
      pipeline.deleteTime = new Date();
      await this.pipelineRepository.save(pipeline);
    }
}
