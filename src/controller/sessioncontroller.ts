import { Authorized, Body, CurrentUser, Delete, Get, HttpCode, JsonController, OnUndefined, Param, Patch, Post, QueryParams } from 'routing-controllers';
import { AddSessionRequest, GetSessionsQuery, Page, SessionResponse } from '../component/types';
import { getRepository } from 'common-db-node/dist/db';
import { StatusCodes } from 'http-status-codes';
import { AuthorizedUser } from '../component/http';
import { isPagination, paginate } from '../component/util';
import { Session } from '../entity/session';
import { Chat } from '../entity/chat';
import { Project } from '../entity/project';
import { Form } from '../entity/form';
import { createAgentSession } from '../component/service/knowledge';

function getResponse(entry: Session): SessionResponse {
  return {
    id: entry.id,
    projectId: entry.projectId,
    name: entry.name,
    details: entry.details,
    customerInfo: entry.customerInfo,
    summary: entry.summary,
    formFilled: entry.formFilled,
    createTime: entry.createTime.toISOString(),
  };
}

@JsonController()
export class SessionController {
  private readonly sessionRepository = getRepository(Session);
  private readonly chatRepository = getRepository(Chat);
  private readonly projectRepository = getRepository(Project);
  private readonly formRepository = getRepository(Form);

  @Get(`/v1/sessions`)
  @Authorized()
  public async index(@CurrentUser() currentUser: AuthorizedUser, @QueryParams() query: GetSessionsQuery): Promise<SessionResponse[] | Page<SessionResponse>> {
    const [sessions, total] = await this.sessionRepository.findAndCount({
      where: { projectId: query.projectId },
      ...isPagination(query) ? paginate(query.pageNumber, query.pageSize) : undefined,
      order: {
        createTime: 'DESC',
      },
    });
    const items = sessions.map(getResponse);
    return isPagination(query) ? { items, total } : items;
  }

  @Post(`/v1/sessions`)
  @HttpCode(StatusCodes.OK)
  public async add(@Body() request: AddSessionRequest): Promise<SessionResponse> {
    const project = await this.projectRepository.findOneByOrFail({
      id: request.projectId,
    });

    // find form
    const form = await this.formRepository.findOneBy({
      projectId: project.id,
    });
    const sessionId = await createAgentSession(project.knowledgeId, project.settings.currentForm === 0 ? form?.url : project.settings.externalFormUrl, project.settings.whatsappAddress);

    const session = await this.sessionRepository.save(this.sessionRepository.create({
      id: sessionId,
      projectId: request.projectId,
      name: request.name,
      details: request.details,
      customerInfo: request.customerInfo,
    }));
    return getResponse(session);
  }

  @Patch(`/v1/sessions/:sessionId`)
  @HttpCode(StatusCodes.OK)
  public async update(@Body() request: { formFilled: boolean }, @Param('sessionId') sessionId: string): Promise<SessionResponse> {
    const session = await this.sessionRepository.findOneByOrFail({
      id: sessionId,
    });
    session.formFilled = request.formFilled;
    return getResponse(await this.sessionRepository.save(session));
  }

  @Delete(`/v1/sessions/:sessionId`)
  @OnUndefined(StatusCodes.NO_CONTENT)
  @Authorized()
  public async delete(@Param('sessionId') sessionId: string): Promise<void> {
    await this.deleteSession(sessionId);
  }

  private async deleteSession(sessionId: string): Promise<void> {
    const session = await this.sessionRepository.findOneByOrFail({
      id: sessionId,
    });

    const chats = await this.chatRepository.find({
      where: { sessionId: session.id },
    });
    for (const chat of chats) {
      chat.deleteTime = new Date();
      await this.chatRepository.save(chat);
    }

    session.deleteTime = new Date();
    await this.sessionRepository.save(session);
  }
}
