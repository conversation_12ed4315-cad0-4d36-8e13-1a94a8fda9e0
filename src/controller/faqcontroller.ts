import { Authorized, Body, CurrentUser, Delete, Get, JsonController, OnUndefined, Param, Patch, Post, QueryParams, UploadedFile } from 'routing-controllers';
import { getRepository } from 'common-db-node/dist/db';
import { AuthorizedUser, badRequest } from '../component/http';
import { Faq } from '../entity/faq';
import { AddFaqRequest, UpdateFaqRequest, GetFaqsQuery, FaqResponse, Page, ChunkMethod } from '../component/types';
import { isNull, isPagination, paginate } from '../component/util';
import { StatusCodes } from 'http-status-codes';
import { ILike } from 'typeorm';
import { Project } from '../entity/project';
import * as path from 'path';
import * as fs from 'fs';
import { deleteKnowledgeDocs, parseKnowledgeDocs, addKnowledgeDocs, updateKnowledgeDocs } from '../component/service/knowledge';
import { config } from '../component/config';

const XLSX = require('xlsx');

function getFaqResponse(faq: Faq): FaqResponse {
  return {
    id: faq.id,
    projectId: faq.projectId,
    question: faq.question,
    answer: faq.answer,
    createTime: faq.createTime.toISOString(),
    updateTime: faq.updateTime.toISOString(),
  };
}

@JsonController()
export class FaqController {
  private readonly faqRepository = getRepository(Faq);
  private readonly projectRepository = getRepository(Project);

  @Get(`/v1/faqs`)
  @Authorized()
  public async index(@CurrentUser() currentUser: AuthorizedUser, @QueryParams() query: GetFaqsQuery): Promise<FaqResponse[] | Page<FaqResponse>> {
    const whereCondition: any = {
      projectId: query.projectId,
    };

    if (query.keyword) {
      whereCondition.question = ILike(`%${query.keyword}%`);
    }

    const [faqs, total] = await this.faqRepository.findAndCount({
      where: whereCondition,
      ...isPagination(query) ? paginate(query.pageNumber, query.pageSize) : undefined,
      order: {
        createTime: 'DESC',
      },
    });

    const items = faqs.map(getFaqResponse);
    return isPagination(query) ? { items, total } : items;
  }

  @Post(`/v1/faqs`)
  @Authorized()
  @OnUndefined(StatusCodes.CREATED)
  public async create(@CurrentUser() currentUser: AuthorizedUser, @Body() request: AddFaqRequest): Promise<FaqResponse> {
    const faq = await this.faqRepository.save(this.faqRepository.create({
      projectId: request.projectId,
      question: request.question,
      answer: request.answer,
    }));

    this.updateFAQ(request.projectId).catch(err => console.error('FAQ更新失败:', err));
    return getFaqResponse(faq);
  }

  @Post(`/v1/uploadfaq`)
  @Authorized()
  @OnUndefined(StatusCodes.CREATED)
  public async upload(@Body() request: { projectId: string },
    @UploadedFile('file', { options: { dest: '/tmp' } }) uploadedFile: Express.Multer.File): Promise<void> {
    if (!uploadedFile.originalname.match(/\.(xlsx|xls)$/)) {
      throw badRequest('仅支持Excel文件格式(.xlsx, .xls)');
    }

    try {
      const workbook = XLSX.readFile(uploadedFile.path);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      // 跳过表头，从第二行开始处理数据
      const dataRows = jsonData.slice(1) as string[][];

      const faqsToInsert = [];
      for (let i = 0; i < dataRows.length; i++) {
        const row = dataRows[i];
        const question = row[0]?.toString().trim();
        const answer = row[1]?.toString().trim();

        // 跳过空行或问题/答案为空的行
        if (!question || !answer) {
          console.warn(`第 ${i + 2} 行数据不完整，跳过处理`);
          continue;
        }

        faqsToInsert.push(this.faqRepository.create({
          projectId: request.projectId,
          question: question,
          answer: answer,
        }));
      }

      await this.faqRepository.save(faqsToInsert);

      this.updateFAQ(request.projectId).catch(err => console.error('FAQ更新失败:', err));
    } catch (error) {
      console.error('Excel文件处理失败:', error);
      throw badRequest('Excel文件格式错误或处理失败');
    } finally {
      if (fs.existsSync(uploadedFile.path)) {
        fs.unlinkSync(uploadedFile.path);
      }
    }
  }

  @Patch('/v1/faqs/:faqId')
  @Authorized()
  public async update(@Param('faqId') faqId: string, @Body() request: UpdateFaqRequest): Promise<FaqResponse> {
    let faq = await this.faqRepository.findOneByOrFail({
      id: faqId,
    });
    
    const updateData: any = {};
    if (request.question !== undefined) {
      updateData.question = request.question;
    }
    if (request.answer !== undefined) {
      updateData.answer = request.answer;
    }
    updateData.updateTime = new Date();

    faq = await this.faqRepository.save(this.faqRepository.merge(faq, updateData));

    await this.updateFAQ(faq.projectId);
    return getFaqResponse(faq);
  }

  @Delete(`/v1/faqs/:faqId`)
  @OnUndefined(StatusCodes.NO_CONTENT)
  @Authorized()
  public async delete(@Param('faqId') faqId: string): Promise<void> {
    const faq = await this.faqRepository.findOneByOrFail({
      id: faqId,
    });
    await this.faqRepository.remove(faq);
    await this.updateFAQ(faq.projectId);
  }

  private async updateFAQ(projectId: string) {
    const project = await this.projectRepository.findOneByOrFail({ id: projectId });

    // add faq to knowledge base, need to save file and upload
    const records = await this.faqRepository.find({
      where: {
        projectId: projectId,
      },
    });

    // delete old knowledge
    if (records.length > 0 && !isNull(records[0].documentId)) {
      try {
        await deleteKnowledgeDocs(project.knowledgeId, records[0].documentId);
      } catch (e) {
        console.log('Error delete knowledge faq:', projectId, e);
      }
    }

    // step 1: read data from db and save to local file
    const generateExcelFile = (records: any[], filePath: string): void => {
      if (!records || records.length === 0) {
        console.log('暂无FAQ数据');
        return;
      }

      const data = records.map(record => [
        record.question || '',
        record.answer || '',
      ]);

      // 创建工作簿和工作表
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.aoa_to_sheet(data);

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, 'FAQ');

      // 写入Excel文件
      XLSX.writeFile(workbook, filePath);
    };

    // step 2: upload file to knowledge base
    const fileName = `faq_${projectId}_${new Date().toISOString().split('T')[0]}.xlsx`;
    const filePath = path.join(config.cache.docPath, fileName);

    const exportDir = path.dirname(filePath);
    if (!fs.existsSync(exportDir)) {
      fs.mkdirSync(exportDir, { recursive: true });
    }

    try {
      // 写入Excel文件
      generateExcelFile(records, filePath);
      console.log(`FAQ导出成功: ${filePath}, 共导出 ${records.length} 条`);

      // 上传Excel文件到知识库
      const uploadResponse = await addKnowledgeDocs(project.knowledgeId, filePath, fileName,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      if (!uploadResponse || !uploadResponse.data || !uploadResponse.data[0]) {
        throw new Error('Upload response is invalid: ' + JSON.stringify(uploadResponse));
      }
      const fileId = uploadResponse.data[0].id;
      await this.faqRepository.update({ projectId: projectId }, { documentId: fileId });
      await updateKnowledgeDocs(project.knowledgeId, fileId, ChunkMethod.qa);
      parseKnowledgeDocs(project.knowledgeId, fileId).catch(err => console.error('解析失败', err));
    } catch (error) {
      console.error('FAQ导出失败:', error);
    }
  }
}
