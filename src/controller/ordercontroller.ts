import { Authorized, CurrentUser, Get, HttpCode, JsonController, Post, Req } from 'routing-controllers';
import { getRepository } from 'common-db-node/dist/db';
import { StatusCodes } from 'http-status-codes';
import { Order } from '../entity/order';
import Stripe from 'stripe';
import { User } from '../entity/user';
import { config } from '../component/config';
import { CurrentProductResponse, IntervalType } from '../component/types';
import { Product } from '../entity/product';
import { AuthorizedUser } from '../component/http';
import dayjs = require('dayjs');

const stripe = new Stripe(config.stripe.api_key);

@JsonController()
export class OrderController {
    private readonly orderRepository = getRepository(Order);
    private readonly userRepository = getRepository(User);

    @Post('/v1/webhook')
    @HttpCode(StatusCodes.OK)
    public async handleWebhook(@Req() req: Request) {
      const sig = (req.headers as any)['stripe-signature'] as string;
      const rawBody = (req as any).rawBody;

      let event: Stripe.Event;

      try {
        if (!sig || Array.isArray(sig)) {
          throw new Error('Missing or invalid Stripe signature');
        }

        event = stripe.webhooks.constructEvent(rawBody, sig, config.stripe.webhook_secret);

        if (event.type === 'checkout.session.completed') {
          const session = event.data.object ;

          const email = session?.customer_details?.email || '<EMAIL>';
          const externalId = session?.id;
          const amount = session?.amount_total || 0;
          const paymentStatus = session?.payment_status || 'unknown';
          const paymentMethods = (session?.payment_method_types || []).join(',');
          const productId = session?.metadata?.productId || 'unknown';

          const user = await this.userRepository.findOneOrFail({
            where: { email: email },
          });

          await this.orderRepository.save(
            this.orderRepository.create({
              user: user ?? null,
              email,
              externalId,
              productId,
              amount,
              paymentStatus,
              paymentMethods,
              rawData: session,
            }),
          );
        }
      } catch (err: any) {
        console.error('Webhook signature verification failed.', err.message);
      }
    }


    
  private readonly productRepository = getRepository(Product);
  @Get(`/v1/current-product`)
  @Authorized()
  public async currentProduct(
    @CurrentUser() currentUser: AuthorizedUser
  ): Promise<CurrentProductResponse> {
    const user = await this.userRepository.findOneByOrFail({
      id: currentUser.id,
    });
    const products = await this.productRepository.find();
    const orders = await this.orderRepository.find({
      where: { user: { id: currentUser.id } },
      order: { createTime: 'ASC' },
    });
    return this.getCurrentProduct(orders, products, user);
  }

  public getCurrentProduct(orders: Order[], products: Product[], user: User) {
    // 如果没有任何订单，返回默认产品 或 ‘Free’，默认有效1个月
    if (orders.length == 0) {
      const defaultProduct = products.find((product) => product.default) || {
        id: 'Free',
        name: 'Free',
      };
      const expireTime = dayjs(user.createTime).add(1, 'month').toDate();
      return {
        productId: defaultProduct.id,
        productName: defaultProduct.name,
        expireTime: expireTime.toISOString(),
      };
    }

    // 获取仍然有效的订单
    const validOrders = orders.filter((order) => {
      const expireTime = this.getOrderExpireTime(order, products);
      return expireTime > new Date();
    });
    const lastOrder = orders[orders.length - 1];

    // 所有订单都过期
    let expireTime: Date;
    if (validOrders.length === 0) {
      expireTime = this.getOrderExpireTime(lastOrder, products);
    } else {
      // 从第一个有效订单的创建日期开始计算，累加后续所有的有效订单的周期时间，计算expireTime， productId为最后一个订单
      expireTime = validOrders.reduce((acc, order) => {
        return this.getOrderExpireTime(order, products, acc);
      }, validOrders[0].createTime);
    }
    const product =
      products.filter((product) => product.id == lastOrder.productId)[0] ||
      products.filter((product) => product.default);
    return {
      productId: lastOrder.id,
      productName: product.name,
      expireTime: expireTime.toISOString(),
    };
  }

  public getOrderExpireTime(
    order: Order,
    products: Product[],
    startDate: Date | undefined = undefined
  ): Date {
    const product =
      products.filter((product) => product.id == order.productId)[0] ||
      products.filter((product) => product.default);
    const start = startDate || order.createTime;
    const days =
      product.interval == IntervalType.Monthly
        ? dayjs(start).add(order.amount, 'months')
        : dayjs(start).add(order.amount, 'year');
    return days.toDate();
  }
}
