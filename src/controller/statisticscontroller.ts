import { Authorized, Get, JsonController, QueryParams } from 'routing-controllers';
import { ChatStatistics, DashboardResponse, GetDashboardQuery, GetSessionsQuery, KnowledgeType } from '../component/types';
import { getRepository } from 'common-db-node/dist/db';
import { Session } from '../entity/session';
import { Chat } from '../entity/chat';
import { File } from '../entity/file';
import { Between, In } from 'typeorm';

@JsonController()
export class StatisticsController {
  private readonly sessionRepository = getRepository(Session);
  private readonly fileRepository = getRepository(File);
  private readonly chatRepository = getRepository(Chat);

  @Get(`/v1/statistics/chat`)
  @Authorized()
  public async chatStatistics(@QueryParams() query: GetSessionsQuery): Promise<ChatStatistics> {
    const baseWhere = {
      projectId: query.projectId,
      ...((query.startTime && query.endTime) && { createTime: Between(new Date(query.startTime), new Date(query.endTime)) }),
    };

    // 获取会话总数
    const [sessions, sessionsTotal] = await this.sessionRepository.findAndCount({
      where: baseWhere,
    });

    // 获取所有会话的ID，用于查询聊天记录
    const sessionIds = sessions.map(session => session.id);

    // 统计聊天消息总数
    let totalChats = 0;
    if (sessionIds.length > 0) {
      totalChats = await this.chatRepository.count({
        where: {
          sessionId: In(sessionIds), // 使用 In 操作符查询多个session
        },
      });
    }

    // 统计不重复的国家数量
    const uniqueCountries = await this.sessionRepository
      .createQueryBuilder('session')
      .select('DISTINCT(session.customerInfo ->> \'country\')', 'country')
      .where('session.projectId = :projectId', { projectId: query.projectId })
      .andWhere('session.customerInfo ->> \'country\' IS NOT NULL')
      .andWhere('session.customerInfo ->> \'country\' != \'\'')
      .getRawMany();

    // 统计不重复的城市数量
    const uniqueCities = await this.sessionRepository
      .createQueryBuilder('session')
      .select('DISTINCT(session.customerInfo ->> \'city\')', 'city')
      .where('session.projectId = :projectId', { projectId: query.projectId })
      .andWhere('session.customerInfo ->> \'city\' IS NOT NULL')
      .andWhere('session.customerInfo ->> \'city\' != \'\'')
      .getRawMany();

    return {
      totalConversations: sessionsTotal,
      totalChats: totalChats,
      totalCountry: uniqueCountries.length,
      totalCity: uniqueCities.length,
    };
  }

  @Get(`/v1/statistics/dashboard`)
  @Authorized()
  public async index(@QueryParams() query: GetDashboardQuery): Promise<DashboardResponse> {
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const baseWhere = {
      ...(query.projectId && { projectId: query.projectId }),
      createTime: (query.startTime && query.endTime)
        ? Between(new Date(query.startTime), new Date(query.endTime))
        : Between(sevenDaysAgo, now), // 默认查询近7天数据
    };
        
    const sessionsTotal = await this.sessionRepository.count({
      where: baseWhere,
    });
    const totalWebsites = await this.fileRepository.count({
      where: {
        type: KnowledgeType.Website,
        projectId: query.projectId,
      },
    });
    const totalDocuments = await this.fileRepository.count({
      where: {
        type: KnowledgeType.Document,
        projectId: query.projectId,
      },
    });

    const conversationTrends = await this.sessionRepository
      .createQueryBuilder('session')
      .select('DATE(session.createTime)', 'date')
      .addSelect('COUNT(*)', 'count')
      .where(baseWhere)
      .groupBy('DATE(session.createTime)')
      .orderBy('DATE(session.createTime)', 'ASC')
      .getRawMany();
      // 转换趋势数据格式
    const conversations = conversationTrends.map(item => ({
      date: new Date(item.date).toISOString().split('T')[0],
      count: parseInt(item.count, 10),
    }));

    // 获取最近5条会话
    const latestConversations = await this.sessionRepository.find({
      where: { projectId: query.projectId },
      order: { createTime: 'DESC' },
      take: 5,
      select: ['id', 'name', 'createTime'],
    });

    // 统计国家/地区排名前5（基于customerInfo.country）
    const topLocationsRaw = await this.sessionRepository
      .createQueryBuilder('session')
      .select('session.customerInfo ->> \'country\'', 'country')
      .addSelect('COUNT(*)', 'count')
      .where(baseWhere)
      .andWhere('session.customerInfo ->> \'country\' IS NOT NULL')
      .andWhere('session.customerInfo ->> \'country\' != \'\'')
      .groupBy('session.customerInfo ->> \'country\'')
      .orderBy('COUNT(*)', 'DESC')
      .limit(5)
      .getRawMany();

    const topLocations = topLocationsRaw.map(item => ({
      country: item.country,
      count: parseInt(item.count, 10),
    }));

    // 统计渠道类型排名（基于details.channel）
    const topChannelsRaw = await this.sessionRepository
      .createQueryBuilder('session')
      .select('session.details ->> \'channel\'', 'channel')
      .addSelect('COUNT(*)', 'count')
      .where(baseWhere)
      .andWhere('session.details ->> \'channel\' IS NOT NULL')
      .andWhere('session.details ->> \'channel\' != \'\'')
      .groupBy('session.details ->> \'channel\'')
      .orderBy('COUNT(*)', 'DESC')
      .getRawMany();

    const topChannels = topChannelsRaw.map(item => ({
      channel: String(item.channel || ''),
      count: parseInt(item.count, 10),
    }));

    return {
      overview: {
        totalConversations: sessionsTotal,
        totalKnowledge: totalWebsites + totalDocuments,
        totalWebsites: totalWebsites,
        totalDocuments: totalDocuments,
        conversations: conversations.length > 0 ? conversations : [{
          date: new Date().toISOString().split('T')[0],
          count: 0,
        }],
      },
      latestConversations: latestConversations.map(session => ({
        id: session.id,
        name: session.name || 'Unnamed Session',
        createTime: session.createTime.toISOString(),
      })),
      topLocations: topLocations.length > 0 ? topLocations : [{
        country: '',
        count: 0,
      }],
      topChannels: topChannels.length > 0 ? topChannels : [{
        channel: 'WebChat',
        count: 0,
      }, {
        channel: 'WhatsApp',
        count: 0,
      }],
    };
  }
}