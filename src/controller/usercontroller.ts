import { Authorized, Body, CurrentUser, Delete, Get, HttpCode, JsonController, OnUndefined, Param, Patch, Post, QueryParams } from 'routing-controllers';
import { StatusCodes } from 'http-status-codes';
import { ILike, In } from 'typeorm';
import { ADMINS, AuthorizedUser, isAdmin } from '../component/http';
import { getRepository } from 'common-db-node/dist/db';
import { hash, isPagination, validateEmail, paginate, randomPassword, toArray, validateRole, verify } from '../component/util';
import { GetUsersQuery, Page, RegisterRequest, ResetUserRequest, ResponseCode, UpdateUserRequest, UserResponse } from '../component/types';
import { User } from '../entity/user';
import { Role } from '../entity/role';
import { badRequest, forbidden } from '../component/http';

const INTERNAL_USER_NAMES = ['root', 'service', 'connector'];

function isInternalUserNames(username: string): boolean {
  return INTERNAL_USER_NAMES.includes(username);
}

function getResponse(user: User): UserResponse {
  return {
    id: user.id,
    username: user.name,
    roles: user.roles.map(r => r.name),
    createTime: user.createTime,
    email: user.email,
    lastLoginTime: user.lastLoginTime,
  };
}

@JsonController()
export class UserController {
  private readonly userRepository = getRepository(User);
  private readonly roleRepository = getRepository(Role);

  @Get(`/v1/users`)
  @Authorized()
  public async index(@CurrentUser() currentUser: AuthorizedUser, @QueryParams() query: GetUsersQuery): Promise<UserResponse[] | Page<UserResponse>> {
    let roleArray;
    if (query.roles) {
      roleArray = query.roles.split(',').map(role => role.trim());
    }

    const [users, total] = await this.userRepository.findAndCount({
      where: {
        ...query.username && { name: ILike(`%${query.username}%`) },
        ...query.ids && { id: In(toArray(query.ids)) },
        ...query.roles && {
          roles: {
            name: In(roleArray ?? []),
          },
        },
      },
      ...isPagination(query) ? paginate(query.pageNumber, query.pageSize) : undefined,
      order: {
        createTime: 'DESC',
      },
    });
    const items = users.map(getResponse);
    return isPagination(query) ? { items, total } : items;
  }

  @Get(`/v1/user`)
  @Authorized()
  public async get(@CurrentUser() currentUser: AuthorizedUser): Promise<UserResponse> {
    const user = await this.userRepository.findOneByOrFail({
      id: currentUser.id,
    });
    /*if (!validateRole(currentUser, user.roles.map(role => role.name))) {
      throw badRequest(ResponseCode.invalid_role);
    }*/
    return getResponse(user);
  }

  @Post(`/v1/users`)
  @HttpCode(StatusCodes.CREATED)
  public async register(@Body() request: RegisterRequest): Promise<UserResponse | void> {
    const username = request.username;
    const email = request.email;
    const password = request.password ?? randomPassword(8);
    const roles = request.roles ?? ['normal_user'];

    if (await this.userRepository.findOneBy({ name: username })) {
      throw badRequest(ResponseCode.username_exist);
    }
    if (email && !validateEmail(email)) {
      throw badRequest(ResponseCode.invalid_email);
    }
    const user = await this.userRepository.save(
      this.userRepository.create({
        name: username,
        password: await hash(password),
        email: email,
        roles: roles.map((role) =>
          this.roleRepository.create({ name: role })),
      }));

    return getResponse(user);
  }

  @Post(`/v1/user/reset`)
  @Authorized(ADMINS)
  @HttpCode(StatusCodes.OK)
  public async resetPassword(@Body() request: ResetUserRequest): Promise<UserResponse> {
    const user = await this.userRepository.findOneBy({
      id: request.id,
    });

    if (user) {
      user.password = await hash(request.password);
      await this.userRepository.save(user);
      return getResponse(user);
    } else {
      throw badRequest(ResponseCode.username_not_exist);
    }
  }

  @Patch(`/v1/users/:userId`)
  @Authorized()
  @HttpCode(StatusCodes.OK)
  public async update(@CurrentUser() currentUser: AuthorizedUser, @Body() request: UpdateUserRequest, @Param('userId') userId: string): Promise<UserResponse> {
    if (!isAdmin(currentUser) && currentUser.id !== userId) {
      throw forbidden(ResponseCode.invalid_user);
    }

    const user = await this.userRepository.findOneByOrFail({
      id: userId,
    });

    // Update username
    if (request.username) {
      user.name = request.username;
    }

    // Update password
    if (request.password) {
      if (!await verify(request.oldPassword ?? '', user.password ?? '')) {
        throw badRequest(ResponseCode.invalid_old_password);
      }
      user.password = await hash(request.password);
    }

    //更新邮箱
    if (request.email && user.email != request.email) {
      const exists = await this.userRepository.existsBy({
        email: request.email,
      });
      if (exists || !validateEmail(request.email)) {
        throw badRequest(ResponseCode.invalid_email);
      }
      user.email = request.email;
    }

    // Update role
    if (request.roles) {
      if (!validateRole(currentUser, request.roles)) {
        throw badRequest(ResponseCode.invalid_role);
      }
      user.roles = request.roles.map(role => this.roleRepository.create({
        name: role,
      }));
    }

    return getResponse(await this.userRepository.save(user));
  }

  @Delete(`/v1/users/:userId`)
  @OnUndefined(StatusCodes.NO_CONTENT)
  @Authorized()
  public async delete(@CurrentUser() currentUser: AuthorizedUser, @Body() request: { password: string }, @Param('userId') userId: string): Promise<void> {
    const user = await this.userRepository.findOneByOrFail({
      id: userId,
    });
    const matched = await verify(request.password, user.password)
    if (!matched) {
      throw badRequest(ResponseCode.invalid_old_password);
    }
    user.deleteTime = new Date();
    await this.userRepository.save(user);
  }
}
