import { MigrationInterface, QueryRunner } from "typeorm";

export class Addemail1757161112089 implements MigrationInterface {
    name = 'Addemail1757161112089'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "service"."email" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "projectId" text NOT NULL, "activated" boolean DEFAULT true, "type" text NOT NULL, "sender" text NOT NULL, "email" text NOT NULL, "authorized" boolean DEFAULT false, "accessToken" text, "expiresAt" TIMESTAMP NOT NULL DEFAULT now(), "companyName" text, "companyAddress" text, "productPage" text, "contactUs" text, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updateTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, "userId" uuid, CONSTRAINT "PK_1e7ed8734ee054ef18002e29b1c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "service"."email" ADD CONSTRAINT "FK_13e97b4a1d6074fd75ea1bb844e" FOREIGN KEY ("userId") REFERENCES "service"."user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."email" DROP CONSTRAINT "FK_13e97b4a1d6074fd75ea1bb844e"`);
        await queryRunner.query(`DROP TABLE "service"."email"`);
    }

}
