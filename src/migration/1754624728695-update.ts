import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1754624728695 implements MigrationInterface {
    name = 'Update1754624728695'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."session" ADD "summary" text`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."session" DROP COLUMN "summary"`);
    }

}
