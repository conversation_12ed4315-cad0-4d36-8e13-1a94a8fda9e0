import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1754372743514 implements MigrationInterface {
    name = 'Update1754372743514'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."form" ALTER COLUMN "logo" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."form" ALTER COLUMN "logo" SET NOT NULL`);
    }

}
