import { MigrationInterface, QueryRunner } from "typeorm";

export class AddProduct1753076868611 implements MigrationInterface {
    name = 'AddProduct1753076868611'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "service"."product" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" text NOT NULL, "description" text NOT NULL, "limits" json, "variants" json, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_bebc9158e480b949565b4dc7a82" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "service"."product"`);
    }

}
