import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateProduct1753078613120 implements MigrationInterface {
    name = 'UpdateProduct1753078613120'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "limits"`);
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "variants"`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "responses" integer`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "bots" integer`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "teamMembers" integer`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "branding" boolean`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "documents" integer`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "pricePerMonth" integer`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "pricePerYear" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "pricePerYear"`);
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "pricePerMonth"`);
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "documents"`);
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "branding"`);
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "teamMembers"`);
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "bots"`);
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "responses"`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "variants" json`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "limits" json`);
    }

}
