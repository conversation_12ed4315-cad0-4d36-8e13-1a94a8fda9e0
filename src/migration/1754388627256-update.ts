import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1754388627256 implements MigrationInterface {
    name = 'Update1754388627256'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."form" ALTER COLUMN "url" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."form" ALTER COLUMN "url" SET NOT NULL`);
    }

}
