import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1752321475575 implements MigrationInterface {
    name = 'Update1752321475575'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "service"."session" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "projectId" text NOT NULL, "name" text, "details" json, "customerInfo" json, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updateTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_f55da76ac1c3ac420f444d2ff11" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service"."chat" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "sessionId" text NOT NULL, "question" text NOT NULL, "answer" json, "reference" json, "like" integer, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_9d0b2ba74336710fd31154738a5" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "service"."chat"`);
        await queryRunner.query(`DROP TABLE "service"."session"`);
    }

}
