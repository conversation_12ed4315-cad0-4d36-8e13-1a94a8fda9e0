import { MigrationInterface, QueryRunner } from "typeorm";

export class AddpipeTemplate1754292547747 implements MigrationInterface {
    name = 'AddpipeTemplate1754292547747'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "service"."pipelineTemplate" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" text NOT NULL, "description" text NOT NULL, "content" text NOT NULL, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_1332fe20ec4609b5c0ef68d5e8b" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "service"."pipelineTemplate"`);
    }

}
