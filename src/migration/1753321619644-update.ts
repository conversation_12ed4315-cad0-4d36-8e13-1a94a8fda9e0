import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1753321619644 implements MigrationInterface {
    name = 'Update1753321619644'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."project" ADD "knowledgeId" text NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."project" DROP COLUMN "knowledgeId"`);
    }

}
