import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1755767889213 implements MigrationInterface {
    name = 'Update1755767889213'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."user" ADD "lastLoginTime" TIMESTAMP WITH TIME ZONE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."user" DROP COLUMN "lastLoginTime"`);
    }

}
