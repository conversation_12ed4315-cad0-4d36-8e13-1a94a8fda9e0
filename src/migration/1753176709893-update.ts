import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1753176709893 implements MigrationInterface {
    name = 'Update1753176709893'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."order" ADD "rawData" json`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."order" DROP COLUMN "rawData"`);
    }

}
