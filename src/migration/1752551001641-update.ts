import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1752551001641 implements MigrationInterface {
    name = 'Update1752551001641'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."file" ALTER COLUMN "size" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "service"."file" ALTER COLUMN "mimeType" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."file" ALTER COLUMN "mimeType" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "service"."file" ALTER COLUMN "size" SET NOT NULL`);
    }

}
