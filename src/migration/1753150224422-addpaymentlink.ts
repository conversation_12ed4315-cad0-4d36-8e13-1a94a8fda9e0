import { MigrationInterface, QueryRunner } from "typeorm";

export class Addpaymentlink1753150224422 implements MigrationInterface {
    name = 'Addpaymentlink1753150224422'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "paymentLink" text`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "paymentLink"`);
    }

}
