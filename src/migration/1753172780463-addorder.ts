import { MigrationInterface, QueryRunner } from "typeorm";

export class Addorder1753172780463 implements MigrationInterface {
    name = 'Addorder1753172780463'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "service"."order" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "externalId" text NOT NULL, "email" text NOT NULL, "productId" text NOT NULL, "amount" integer NOT NULL, "paymentStatus" text NOT NULL, "paymentMethods" text NOT NULL, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, "userId" uuid, CONSTRAINT "PK_1031171c13130102495201e3e20" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "pricePerMonth"`);
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "pricePerYear"`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "price" integer`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "interval" text NOT NULL`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "additionalInfo" text`);
        await queryRunner.query(`ALTER TABLE "service"."order" ADD CONSTRAINT "FK_caabe91507b3379c7ba73637b84" FOREIGN KEY ("userId") REFERENCES "service"."user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."order" DROP CONSTRAINT "FK_caabe91507b3379c7ba73637b84"`);
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "additionalInfo"`);
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "interval"`);
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "price"`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "pricePerYear" integer`);
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "pricePerMonth" integer`);
        await queryRunner.query(`DROP TABLE "service"."order"`);
    }

}
