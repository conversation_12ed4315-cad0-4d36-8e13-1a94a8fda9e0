import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1752325208062 implements MigrationInterface {
    name = 'Update1752325208062'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."chat" DROP COLUMN "answer"`);
        await queryRunner.query(`ALTER TABLE "service"."chat" ADD "answer" text`);
        await queryRunner.query(`ALTER TABLE "service"."chat" DROP COLUMN "reference"`);
        await queryRunner.query(`ALTER TABLE "service"."chat" ADD "reference" json`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."chat" DROP COLUMN "reference"`);
        await queryRunner.query(`ALTER TABLE "service"."chat" ADD "reference" jsonb`);
        await queryRunner.query(`ALTER TABLE "service"."chat" DROP COLUMN "answer"`);
        await queryRunner.query(`ALTER TABLE "service"."chat" ADD "answer" jsonb`);
    }

}
