import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1752325098640 implements MigrationInterface {
    name = 'Update1752325098640'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."chat" DROP COLUMN "answer"`);
        await queryRunner.query(`ALTER TABLE "service"."chat" ADD "answer" jsonb`);
        await queryRunner.query(`ALTER TABLE "service"."chat" DROP COLUMN "reference"`);
        await queryRunner.query(`ALTER TABLE "service"."chat" ADD "reference" jsonb`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."chat" DROP COLUMN "reference"`);
        await queryRunner.query(`ALTER TABLE "service"."chat" ADD "reference" json`);
        await queryRunner.query(`ALTER TABLE "service"."chat" DROP COLUMN "answer"`);
        await queryRunner.query(`ALTER TABLE "service"."chat" ADD "answer" json`);
    }

}
