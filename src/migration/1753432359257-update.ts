import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1753432359257 implements MigrationInterface {
    name = 'Update1753432359257'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "default" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "default"`);
    }

}
