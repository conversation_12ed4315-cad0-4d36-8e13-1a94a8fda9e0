import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1753078795639 implements MigrationInterface {
    name = 'Update1753078795639'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "popular" integer DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "popular"`);
    }

}
