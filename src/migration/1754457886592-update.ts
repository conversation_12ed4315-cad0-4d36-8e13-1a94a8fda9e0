import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1754457886592 implements MigrationInterface {
    name = 'Update1754457886592'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."faq" ADD "documentId" text`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."faq" DROP COLUMN "documentId"`);
    }

}
