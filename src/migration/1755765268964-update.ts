import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1755765268964 implements MigrationInterface {
    name = 'Update1755765268964'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."session" ADD "formFilled" boolean DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."session" DROP COLUMN "formFilled"`);
    }

}
