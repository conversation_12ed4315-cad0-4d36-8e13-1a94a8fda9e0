import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1753079941009 implements MigrationInterface {
    name = 'Update1753079941009'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."product" ADD "type" text NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."product" DROP COLUMN "type"`);
    }

}
