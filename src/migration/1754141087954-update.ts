import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1754141087954 implements MigrationInterface {
    name = 'Update1754141087954'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "service"."submission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "formId" text NOT NULL, "content" text NOT NULL, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_7faa571d0e4a7076e85890c9bd0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service"."pipeline" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "projectId" text NOT NULL, "pipeline" text NOT NULL, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updateTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, "userId" uuid, CONSTRAINT "PK_df8aedd50509192d995535d68cd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service"."formTemplate" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" text NOT NULL, "description" text NOT NULL, "logo" text NOT NULL, "content" text NOT NULL, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_c92746746868c8960dd260f6cf9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service"."form" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "projectId" text NOT NULL, "name" text NOT NULL, "description" text NOT NULL, "logo" text NOT NULL, "content" text NOT NULL, "url" text NOT NULL, "active" boolean DEFAULT false, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updateTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, "userId" uuid, CONSTRAINT "PK_8f72b95aa2f8ba82cf95dc7579e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "service"."pipeline" ADD CONSTRAINT "FK_f848b9f4b302cf221aea97b9b69" FOREIGN KEY ("userId") REFERENCES "service"."user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service"."form" ADD CONSTRAINT "FK_f91337a182c92d0af3fd648d63e" FOREIGN KEY ("userId") REFERENCES "service"."user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."form" DROP CONSTRAINT "FK_f91337a182c92d0af3fd648d63e"`);
        await queryRunner.query(`ALTER TABLE "service"."pipeline" DROP CONSTRAINT "FK_f848b9f4b302cf221aea97b9b69"`);
        await queryRunner.query(`DROP TABLE "service"."form"`);
        await queryRunner.query(`DROP TABLE "service"."formTemplate"`);
        await queryRunner.query(`DROP TABLE "service"."pipeline"`);
        await queryRunner.query(`DROP TABLE "service"."submission"`);
    }

}
