import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1757245940783 implements MigrationInterface {
    name = 'Update1757245940783'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."email" ADD "refreshToken" text`);
        await queryRunner.query(`ALTER TABLE "service"."email" DROP COLUMN "expiresAt"`);
        await queryRunner.query(`ALTER TABLE "service"."email" ADD "expiresAt" TIMESTAMP WITH TIME ZONE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."email" DROP COLUMN "expiresAt"`);
        await queryRunner.query(`ALTER TABLE "service"."email" ADD "expiresAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "service"."email" DROP COLUMN "refreshToken"`);
    }

}
