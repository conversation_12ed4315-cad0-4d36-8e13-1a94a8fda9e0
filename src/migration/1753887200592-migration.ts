import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1753887200592 implements MigrationInterface {
    name = 'Migration1753887200592'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "service"."faq" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "projectId" text NOT NULL, "question" text NOT NULL, "answer" text NOT NULL, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updateTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_d6f5a52b1a96dd8d0591f9fbc47" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "service"."faq"`);
    }

}
