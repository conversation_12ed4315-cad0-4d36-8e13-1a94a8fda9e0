import { MigrationInterface, QueryRunner } from "typeorm";

export class Initial1752116856929 implements MigrationInterface {
    name = 'Initial1752116856929'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "service"."role" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" text NOT NULL, "userId" uuid, CONSTRAINT "PK_b36bcfe02fc8de3c57a8b2391c2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service"."user" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" text NOT NULL, "password" text, "phoneNo" text, "email" text, "googleSub" text, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service"."project" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" text NOT NULL, "website" text NOT NULL, "settings" json, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, "userId" uuid, CONSTRAINT "PK_4d68b1358bb5b766d3e78f32f57" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service"."file" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "projectId" text NOT NULL, "name" text NOT NULL, "type" text NOT NULL, "size" bigint NOT NULL, "mimeType" text NOT NULL, "path" text NOT NULL, "status" text DEFAULT 'Parsing', "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updateTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, "folderId" uuid, CONSTRAINT "PK_36b46d232307066b3a2c9ea3a1d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "service"."folder" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" text NOT NULL, "projectId" text NOT NULL, "createTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updateTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleteTime" TIMESTAMP WITH TIME ZONE, "parentId" uuid, CONSTRAINT "PK_6278a41a706740c94c02e288df8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "service"."role" ADD CONSTRAINT "FK_3e02d32dd4707c91433de0390ea" FOREIGN KEY ("userId") REFERENCES "service"."user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service"."project" ADD CONSTRAINT "FK_7c4b0d3b77eaf26f8b4da879e63" FOREIGN KEY ("userId") REFERENCES "service"."user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service"."file" ADD CONSTRAINT "FK_3563fb0d3e9557359f541ac77da" FOREIGN KEY ("folderId") REFERENCES "service"."folder"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service"."folder" ADD CONSTRAINT "FK_9ee3bd0f189fb242d488c0dfa39" FOREIGN KEY ("parentId") REFERENCES "service"."folder"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "service"."folder" DROP CONSTRAINT "FK_9ee3bd0f189fb242d488c0dfa39"`);
        await queryRunner.query(`ALTER TABLE "service"."file" DROP CONSTRAINT "FK_3563fb0d3e9557359f541ac77da"`);
        await queryRunner.query(`ALTER TABLE "service"."project" DROP CONSTRAINT "FK_7c4b0d3b77eaf26f8b4da879e63"`);
        await queryRunner.query(`ALTER TABLE "service"."role" DROP CONSTRAINT "FK_3e02d32dd4707c91433de0390ea"`);
        await queryRunner.query(`DROP TABLE "service"."folder"`);
        await queryRunner.query(`DROP TABLE "service"."file"`);
        await queryRunner.query(`DROP TABLE "service"."project"`);
        await queryRunner.query(`DROP TABLE "service"."user"`);
        await queryRunner.query(`DROP TABLE "service"."role"`);
    }

}
