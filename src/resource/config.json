{"db": {"url": "postgresql://dummy:dummy@localhost:5432/chat-assist", "schema": "service"}, "settings": {"expireIn": 86400, "prefix": "https://www.yapyapbot.com/form/", "redirectUrl": "https://www.yapyapbot.com/login"}, "cache": {"videoClipPath": "/var/terminus/chat-assist/clips/video", "audioClipPath": "/var/terminus/chat-assist/clips/audio", "imageClipPath": "/var/terminus/chat-assist/clips/image", "docPath": "/var/terminus/chat-assist/docs"}, "proxy": {"clipUrl": "http://localhost:10070/static"}, "knowledgeBase": {"api_key": "ragflow-YzNmMwZTNlNzJhNzExZjA5ZDY0MDI0Mm", "dataset": "http://************:81/api/v1/datasets", "agent_id": "a43cf2e87e6911f0b23d0242c0a86006", "agent_url": "http://************:81/api/v1/agents/", "minerU_url": "http://*************:8002/file_parse"}, "textIn": {"url": "https://api.textin.com/ai/service/v1/pdf_to_markdown", "appId": "acaa5cf0a1bc95c82085fdadd51e3fb5", "secretCode": "141bd27b7a1a35aaa496421b769a89f0"}, "fireCrawl": {"url": "http://************:3002", "api_key": "", "pages_limit": 50}, "SSO": {"google": {"client_id": "*********************************************.apps.googleusercontent.com", "client_secret": "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", "login_redirect_uri": "https://www.yapyapbot.com/v1/sso/google/callback", "gmail_redirect_uri": "https://flowered-bloodlike-vallie.ngrok-free.app/v1/auth/gmail/callback"}}, "stripe": {"api_key": "xxxx", "webhook_secret": "xxxx"}, "aliyunOSS": {"region": "oss-ap-southeast-1", "bucket": "ai-chatbot-sg", "accessKeyId": "LTAI5tKnAGfCjiFA4kT2WD2A", "accessKeySecret": "******************************"}, "openai": {"api_key": "********************************************************************************************************************************************************************"}, "aliyunEmail": {"endpoint": "dm.ap-southeast-1.aliyuncs.com", "accessKeyId": "LTAI5t8uezDpYf1urE3yKg9x", "accessKeySecret": "******************************", "accountName": "<EMAIL>"}}