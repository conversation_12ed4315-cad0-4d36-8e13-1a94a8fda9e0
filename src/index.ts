import 'reflect-metadata';
import { initializeDb } from 'common-db-node/dist/db';
import { initializeHttpServer } from './component/http';
import { config, dbOptions, httpOptions } from './component/config';
import { createCacheDir } from './component/util';
import { Execution } from './component/execution';


(async () => {
  await initializeDb(dbOptions);
  createCacheDir(config.cache.videoClipPath);
  createCacheDir(config.cache.audioClipPath);
  createCacheDir(config.cache.imageClipPath);
  createCacheDir(config.cache.docPath);
  await initializeHttpServer(await httpOptions());

  const execution = new Execution();
  execution.scheduleTask();
})().catch(error => {
  console.error(`Failed to start application`, error);
});
