import { BaseEntity, Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { CustomerInfo, SessionDetails } from '../component/types';

@Entity('session')
export class Session extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('text', { nullable: false })
  projectId: string;

  @Column('text', { nullable: true })
  name: string;

  @Column('json', { nullable: true })
  details: SessionDetails;

  @Column('json', { nullable: true })
  customerInfo: CustomerInfo;

  @Column('text', { nullable: true })
  summary: string;

  @Column('boolean', { nullable: true, default: false })
  formFilled: boolean;

  @CreateDateColumn({ type: 'timestamp with time zone', nullable: false, default: () => 'now()' })
  createTime: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updateTime: Date;

  @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
  deleteTime: Date | null;
}
