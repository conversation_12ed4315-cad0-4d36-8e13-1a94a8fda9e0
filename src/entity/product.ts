import { BaseEntity, Column, CreateDate<PERSON><PERSON>umn, DeleteDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm';
import {IntervalType, ProductType} from '../component/types';

@Entity('product')
export class Product extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column('boolean', { nullable: false, default: false })
    default: boolean;

    @Column('text', { nullable: false })
    name: string;

    @Column('text', { nullable: false })
    type: ProductType;

    @Column('text', { nullable: false })
    description: string;

    @Column('integer', { nullable: true })
    responses: number;

    @Column('integer', { nullable: true })
    bots: number;

    @Column('integer', { nullable: true })
    teamMembers: number;

    @Column('boolean', { nullable: true })
    branding: boolean;

    @Column('integer', { nullable: true })
    documents: number;

    @Column('integer', { nullable: true, default: 0 })
    popular: number;

    @Column('integer', { nullable: true })
    price: number;

    @Column('text', { nullable: false })
    interval: IntervalType;

    @Column('text', { nullable: true })
    paymentLink: string;

    @Column('text', { nullable: true })
    additionalInfo: string;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false, default: () => 'now()' })
    createTime: Date;

    @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
    deleteTime: Date | null;
}
