import { BaseEntity, Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user';

@Entity('role')
export class Role extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column('text', { nullable: false })
    name: string;

    @ManyToOne(() => User, user => user.id, { orphanedRowAction: 'delete', onDelete: 'CASCADE' })
    user: User;
}
