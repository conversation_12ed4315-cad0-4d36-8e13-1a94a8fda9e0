import { BaseEntity, Column, CreateDateColumn, DeleteDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user';
import { Settings } from '../component/types';

@Entity('project')
export class Project extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => User, user => user.id, { orphanedRowAction: 'delete', onDelete: 'CASCADE' })
    user: User;

    @Column('text', { nullable: false })
    name: string;

    @Column('text', { nullable: false })
    website: string;

    @Column('text', { nullable: false })
    knowledgeId: string;

    @Column('json', { nullable: true })
    settings: Settings;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false, default: () => 'now()' })
    createTime: Date;

    @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
    deleteTime: Date | null;
}
