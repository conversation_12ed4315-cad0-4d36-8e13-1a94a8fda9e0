import { <PERSON><PERSON>ntity, Column, CreateDate<PERSON><PERSON>umn, DeleteDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Folder } from './folder';
import { FileParseStatus, KnowledgeType } from '../component/types';

@Entity('file')
export class File extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column('text')
    projectId: string;

    @Column('text', { nullable: false })
    name: string;

    @Column('text', { nullable: false })
    type: KnowledgeType;

    @Column('bigint', { nullable: true })
    size: number;

    @Column('text', { nullable: true })
    mimeType: string;

    @Column('text', { nullable: false })
    path: string;

    @Column('text', { nullable: true, default: FileParseStatus.Parsing })
    status: FileParseStatus;

    @ManyToOne(() => Folder, { nullable: true })
    folder: Folder;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false, default: () => 'now()' })
    createTime: Date;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false })
    updateTime: Date;

    @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
    deleteTime: Date | null;
}
