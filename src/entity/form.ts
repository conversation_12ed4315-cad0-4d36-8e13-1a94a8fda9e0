import { BaseEntity, Column, CreateDateColumn, DeleteDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user';

@Entity('form')
export class Form extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => User, user => user.id, { orphanedRowAction: 'delete', onDelete: 'CASCADE' })
    user: User;

    @Column('text', { nullable: false })
    projectId: string;

    @Column('text', { nullable: false })
    name: string;

    @Column('text', { nullable: false })
    description: string;

    @Column('text', { nullable: true })
    logo: string;

    @Column('text', { nullable: false })
    content: string;

    @Column('text', { nullable: true })
    url: string;

    @Column('boolean', { nullable: true, default: false })
    active: boolean;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false, default: () => 'now()' })
    createTime: Date;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false })
    updateTime: Date;

    @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
    deleteTime: Date | null;
}
