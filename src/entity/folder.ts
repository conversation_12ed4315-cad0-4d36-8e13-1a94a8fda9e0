import { Column, CreateDateColumn, DeleteDateColumn, Entity, ManyToOne, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { File } from './file';

@Entity()
export class Folder {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('text')
  name: string;

  @Column('text')
  projectId: string;

  @ManyToOne(() => Folder, { nullable: true })
  parent: Folder | null;

  @OneToMany(() => Folder, folder => folder.parent)
  folders: Folder[];

  @OneToMany(() => File, file => file.folder)
  files: File[];

  @CreateDateColumn({ type: 'timestamp with time zone' })
  createTime: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updateTime: Date;

  @DeleteDateColumn({ type: 'timestamp with time zone' })
  deleteTime: Date;
}
