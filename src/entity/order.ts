import { BaseEntity, Column, CreateDateColumn, DeleteDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user';

@Entity('order')
export class Order extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => User, user => user.id, { orphanedRowAction: 'delete', onDelete: 'CASCADE' })
    user: User;

    @Column('text', { nullable: false })
    externalId: string;

    @Column('text', { nullable: false })
    email: string;

    @Column('text', { nullable: false })
    productId: string;

    @Column('integer', { nullable: false })
    amount: number;

    @Column('text', { nullable: false })
    paymentStatus: string;

    @Column('text', { nullable: false })
    paymentMethods: string;

    @Column('json', { nullable: true })
    rawData: unknown;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false, default: () => 'now()' })
    createTime: Date;

    @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
    deleteTime: Date | null;
}
