import { BaseEntity, Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('chat')
export class Chat extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column('text', { nullable: false })
    sessionId: string;

    @Column('text', { nullable: false })
    question: string;

    @Column('text', { nullable: true })
    answer: string;

    @Column('json', { nullable: true })
    reference: unknown;

    @Column('integer', { nullable: true })
    like: number;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false, default: () => 'now()' })
    createTime: Date;

    @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
    deleteTime: Date | null;
}
