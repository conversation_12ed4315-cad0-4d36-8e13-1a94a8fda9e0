import { BaseEntity, Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('formTemplate')
export class FormTemplate extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column('text', { nullable: false })
    name: string;

    @Column('text', { nullable: false })
    description: string;

    @Column('text', { nullable: false })
    logo: string;

    @Column('text', { nullable: false })
    content: string;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false, default: () => 'now()' })
    createTime: Date;

    @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
    deleteTime: Date | null;
}
