import { BaseEntity, Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('faq')
export class Faq extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column('text', { nullable: false })
    projectId: string;

    @Column('text', { nullable: true })
    documentId: string;

    @Column('text', { nullable: false })
    question: string;

    @Column('text', { nullable: false })
    answer: string;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false, default: () => 'now()' })
    createTime: Date;

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false })
    updateTime: Date;

    @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
    deleteTime: Date | null;
}
