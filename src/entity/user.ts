import { <PERSON><PERSON><PERSON>ty, <PERSON>umn, CreateDate<PERSON><PERSON>umn, DeleteDateColumn, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { Role } from './role';

@Entity('user')
export class User extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column('text')
    name: string;

    @Column('text', { nullable: true })
    password: string;

    @Column('text', { nullable: true })
    phoneNo: string;

    @Column('text', { nullable: true })
    email: string;

    @Column('text', { nullable: true })
    googleSub: string;

    @OneToMany(() => Role, role => role.user, { cascade: true, eager: true })
    roles: Role[];

    @CreateDateColumn({ type: 'timestamp with time zone', nullable: false, default: () => 'now()' })
    createTime: Date;

    @DeleteDateColumn({ type: 'timestamp with time zone', nullable: true })
    deleteTime: Date | null;

    @Column({ type: 'timestamp with time zone', nullable: true })
    lastLoginTime: Date | null;
}
