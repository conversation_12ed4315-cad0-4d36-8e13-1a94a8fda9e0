import cors from 'cors';
import express from 'express';
import { createServer, Server, STATUS_CODES } from 'http';
import jwt from 'jsonwebtoken';
import { Action, HttpError, useExpressServer } from 'routing-controllers';
import type { Authorization<PERSON>he<PERSON> } from 'routing-controllers/types/AuthorizationChecker';
import type { CurrentUserChecker } from 'routing-controllers/types/CurrentUserChecker';
import { StatusCodes } from 'http-status-codes';
import { config } from './config';

const { createProxyMiddleware } = require('http-proxy-middleware');

export class AppError extends Error {
  public code: string;
  public httpCode: number;
  public data: unknown;

  constructor(code: string, message?: string, httpCode?: number, data?: unknown) {
    super(message);
    this.code = code;
    this.httpCode = httpCode ?? StatusCodes.BAD_REQUEST;
    this.data = data;
  }
}

export function badRequest(code: string, message?: string, data?: unknown): AppError {
  return new AppError(code, message, StatusCodes.BAD_REQUEST, data);
}

export function notFound(code: string, message?: string, data?: unknown): AppError {
  return new AppError(code, message, StatusCodes.NOT_FOUND, data);
}

export function unauthorized(code: string, message?: string, data?: unknown): AppError {
  return new AppError(code, message, StatusCodes.UNAUTHORIZED, data);
}

export function forbidden(code: string, message?: string, data?: unknown): AppError {
  return new AppError(code, message, StatusCodes.FORBIDDEN, data);
}


export interface AuthorizedUser {
    id: string;
    name: string;
    roles: string[];
    tenantId?: string;
    projectId?: string;
  }

export const ROOT_USER = 'root_user';

export const SERVICE_USER = 'service_user';

export const CONNECTOR_USER = 'connector_user';

export const SUPER_ADMIN = 'super_admin';

export const TENANT_ADMIN = 'tenant_admin';

export const ADMIN_USER = 'admin_user';

export const NORMAL_USER = 'normal_user';

export const SUPERS = [ROOT_USER, SUPER_ADMIN];

export const TENANT_ADMINS = [...SUPERS, TENANT_ADMIN];

export const ADMINS = [...TENANT_ADMINS, ADMIN_USER];

export const USERS = [...ADMINS, NORMAL_USER];

export const INTERNAL_USERS = [ROOT_USER, SERVICE_USER, CONNECTOR_USER];

export function isRoot(user: AuthorizedUser): boolean {
  return user.roles.includes(ROOT_USER);
}

export function isSuper(user: AuthorizedUser): boolean {
  return user.roles.some(r => SUPERS.includes(r));
}

export function isTenantAdmin(user: AuthorizedUser): boolean {
  return user.roles.some(r => TENANT_ADMINS.includes(r));
}

export function isAdmin(user: AuthorizedUser): boolean {
  return user.roles.some(r => ADMINS.includes(r));
}

export function isInternalUser(user: AuthorizedUser): boolean {
  return user.roles.some(r => INTERNAL_USERS.includes(r));
}


export interface JwtPayload {
  sub: string;
  name: string;
  exp: number;
  roles: string[];
  tenantId?: string;
  projectId?: string;
}

export interface HttpServerOptions {
  port: number;
  application: string;
  controllers: string[];
  publicKey?: string;
}

const requestLogger = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.info(`Receiving ${req.method} request ${req.path}`);
  next();
};

function authorizationChecker(publicKey: string): AuthorizationChecker {
  return async (action: Action, roles: string[]): Promise<boolean> => {
    let token: string | undefined = action.request.headers['authorization'];
    if (!token || !/^Bearer\s/i.test(token)) {
      throw unauthorized('token_empty');
    }
    token = token.replace(/^Bearer\s/i, '');
    return new Promise((resolve, reject) => {
      jwt.verify(token as string, publicKey, (error, decoded) => {
        if (error) {
          console.error(error);
          return reject(unauthorized('token_expired'));
        }
        const payload = decoded as JwtPayload;
        if (roles.length > 0 && roles.every(r => !payload.roles.includes(r))) {
          resolve(false);
        } else {
          resolve(true);
        }
      });
    });
  };
}

function currentUserChecker(): CurrentUserChecker {
  return async (action: Action): Promise<AuthorizedUser> => {
    let token: string | undefined = action.request.headers['authorization'];
    if (!token || !/^Bearer\s/.test(token)) {
      throw unauthorized('token_empty');
    }
    token = token.replace(/^Bearer\s/i, '');
    const payload = jwt.decode(token) as JwtPayload;
    return {
      id: payload.sub,
      name: payload.name,
      roles: payload.roles,
      tenantId: payload.tenantId || action.request.header('x-tenant') || undefined,
      projectId: payload.projectId || action.request.header('x-project') || undefined,
    };
  };
}

export function getTenant(user: AuthorizedUser): string | undefined {
  return user.tenantId === '*' ? undefined : user.tenantId;
}

export function getProject(user: AuthorizedUser): string | undefined {
  return user.projectId === '*' ? undefined : user.projectId;
}

export async function initializeHttpServer(options: HttpServerOptions): Promise<Server> {
  const app = express();
  app.use(express.json({ limit: '1000mb' }));
  app.use(cors());
  app.use(requestLogger);
  app.use('/static/video', express.static(config.cache.videoClipPath));
  app.use('/static/audio', express.static(config.cache.audioClipPath));
  app.use('/static/image', express.static(config.cache.imageClipPath));

  const server = createServer(useExpressServer(app, {
    controllers: options.controllers,
    authorizationChecker: options.publicKey ? authorizationChecker(options.publicKey) : undefined,
    currentUserChecker: options.publicKey ? currentUserChecker() : undefined,
    defaultErrorHandler: false,
  }));

  // health check
  app.get('/', (req, res) => {
    res.status(200).json({
      'application': options.application,
    });
  });

  // error handler
  app.use((err: Error | HttpError | AppError, req: express.Request, res: express.Response, next: express.NextFunction) => {
    console.error(err);
    if (err instanceof AppError) {
      res.status(err.httpCode).json({ code: err.code, message: err.message || undefined, data: err.data });
    } else if ('httpCode' in err) {
      res.status(err.httpCode).json({ code: STATUS_CODES[err.httpCode] || 'unknown', message: err.message });
    } else {
      res.status(500).json({ code: 'internal_server_error', message: process.env.NODE_ENV === 'production' ? undefined : err.message });
    }
    next();
  });

  return new Promise((resolve => {
    server.listen(options.port, () => {
      console.log(`Http server is running on http://localhost:${options.port}`);
      resolve(server);
    });
  }));
}
