import * as $OpenApi from '@alicloud/openapi-client';
import * as $Util from '@alicloud/tea-util';
import * as $Dm20151123 from '@alicloud/dm20151123';
import { config } from '../config';

export interface SendMailOptions {
  to: string;         // 收件人邮箱
  subject: string;    // 邮件标题
  htmlBody?: string;  // HTML 内容
  textBody?: string;  // 纯文本内容
  tagName?: string;   // 阿里云邮件标签
}

export async function sendMail(options: SendMailOptions) {
  // 1. 创建配置
  const APIConfig = new $OpenApi.Config({
    accessKeyId: config.aliyunEmail.accessKeyId,
    accessKeySecret: config.aliyunEmail.accessKeySecret,
  });
  APIConfig.endpoint = config.aliyunEmail.endpoint;

  // 2. 创建客户端
  const client = new $Dm20151123.default(APIConfig);

  // 3. 构建邮件请求
  const singleSendMailRequest = new $Dm20151123.SingleSendMailRequest({
    addressType: 1,
    accountName: config.aliyunEmail.accountName,
    tagName: options.tagName || 'Welcome',             // 默认标签
    replyToAddress: true,
    toAddress: options.to,
    subject: options.subject,
    htmlBody: options.htmlBody || '',
    textBody: options.textBody || '',
  });

  const runtime = new $Util.RuntimeOptions({});

  // 4. 发送邮件
  try {
    const result = await client.singleSendMailWithOptions(singleSendMailRequest, runtime);
    console.log('✅ 邮件发送成功:', result);
    return result;
  } catch (error: any) {
    console.error('❌ 邮件发送失败:', error.message);
    if (error.data?.Recommend) {
      console.error('诊断建议:', error.data.Recommend);
    }
    throw error;
  }
}
