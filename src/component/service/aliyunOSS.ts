import { config } from '../config';
import * as fs from 'fs';

const aliyunOSS = require('ali-oss');

const aliyunOSSclient = new aliyunOSS({
  region: config.aliyunOSS.region,                           // 示例：'oss-cn-hangzhou'，填写Bucket所在地域。
  accessKeyId: config.aliyunOSS.accessKeyId,                // 确保已设置环境变量OSS_ACCESS_KEY_ID。
  accessKeySecret: config.aliyunOSS.accessKeySecret,        // 确保已设置环境变量OSS_ACCESS_KEY_SECRET。
  bucket: config.aliyunOSS.bucket,                           // 示例：'my-bucket-name'，填写存储空间名称。
});

export async function uploadToAliyunOSS(ossFileName: string, content: string, isFile: boolean): Promise<string | undefined> {
  try {
    const fileStream = isFile ? fs.createReadStream(content) : Buffer.from(content.replace(/^data:image\/\w+;base64,/, ''), 'base64');

    const result = await aliyunOSSclient.put(ossFileName, fileStream);
    const privateUrl = result.url;
    const publicUrl = aliyunOSSclient.signatureUrl(ossFileName, { expires: 60 * 60 * 24 * 365 * 10 });
    console.log('File uploaded to aliyun OSS successfully:', privateUrl, publicUrl);
    return publicUrl;
  } catch (err) {
    console.error('Error uploading file to aliyun OSS:', err);
  }
  return undefined;
}

export async function deleteFromAliyunOSS(ossFileName: string) {
  try {
    const result = await aliyunOSSclient.delete(ossFileName);
    console.log('delete file OSS successfully:', ossFileName, result);
  } catch (err) {
    console.error('Error uploading file to aliyun OSS:', err);
  }
}
