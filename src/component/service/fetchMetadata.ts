import axios from 'axios';
import * as cheerio from 'cheerio';
import { URL } from 'url';

export interface Metadata {
  title: string | null;
  description: string | null;
  logo: string | null;  // 网站logo(favicon)
  url: string;
}

/**
 * 抓取网页元数据
 * @param targetUrl 网页链接
 */
export async function fetchMetadata(targetUrl: string): Promise<Metadata> {
  try {
    const { data: html } = await axios.get(targetUrl, {
      headers: {
        'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0 Safari/537.36',
      },
    });

    const $ = cheerio.load(html);
    const baseUrl = new URL(targetUrl);

    // title
    const title =
        $('meta[property="og:title"]').attr('content') ||
        $('title').text() ||
        baseUrl.hostname;

    // description
    const description =
        $('meta[property="og:description"]').attr('content') ||
        $('meta[name="description"]').attr('content') ||
        null;

    // logo: link rel="icon" / apple-touch-icon
    let logo =
        $('link[rel="icon"]').attr('href') ||
        $('link[rel="shortcut icon"]').attr('href') ||
        $('link[rel="apple-touch-icon"]').attr('href') ||
        null;

    // 处理相对路径
    if (logo && !/^https?:\/\//i.test(logo)) {
      logo = new URL(logo, baseUrl).href;
    }

    return {
      title,
      description,
      logo,
      url: targetUrl,
    };
  } catch (error) {
    console.error('fetchMetadata error:', error, targetUrl);
    return {
      title: null,
      description: null,
      logo: null,
      url: targetUrl,
    };
  }
}
