import FirecrawlApp, { CrawlParams, CrawlStatusResponse } from '@mendable/firecrawl-js';
import { config } from '../config';
import { randomUUID } from 'crypto';
import * as path from 'path';
import * as fs from 'fs';

const firecrawlApp = new FirecrawlApp({ apiKey: config.fireCrawl.api_key, apiUrl: config.fireCrawl.url });

export async function crawlWebsite(websiteUrl: string): Promise<Array<{ filePath: string; fileName: string; pageUrl: string; pageTitle?: string }>> {
  const crawlResponse = await firecrawlApp.crawlUrl(websiteUrl, {
    limit: config.fireCrawl.pages_limit,
    ignoreSitemap: true,
    allowExternalLinks: false,
    scrapeOptions: {
      formats: ['markdown'],
      excludeTags: ['img', 'picture', 'figure', 'svg', '#footer', '.sidebar', '.ad', 'script', '.image', '.photo', '.gallery', '.thumbnail'],
    },
  });

  if (!crawlResponse.success) {
    throw new Error(`Failed to crawl: ${crawlResponse.error}`);
  }

  const pageFiles: Array<{
    filePath: string;
    fileName: string;
    pageUrl: string;
    pageTitle?: string;
}> = [];

  // 为每个页面创建单独的文件
  for (let i = 0; i < crawlResponse.data.length; i++) {
    const pageData = crawlResponse.data[i];

    if (pageData.markdown) {
      const fileName = `${randomUUID()}.txt`;
      const filePath = path.join(config.cache.docPath, fileName);
      const pageUrl = pageData.metadata?.sourceURL || pageData.metadata?.url || `${websiteUrl}_page_${i + 1}`;
      const pageTitle = pageData.metadata?.title;

      try {
        await fs.promises.writeFile(filePath, pageData.markdown, 'utf8');
        pageFiles.push({ filePath, fileName, pageUrl, pageTitle });
        console.log(`Saved page to file: ${fileName} (${pageUrl})`);
      } catch (error) {
        console.error(`Failed to save markdown file ${filePath}:`, error);
      }
    }
  }

  if (pageFiles.length === 0) {
    console.log('No markdown content found to save');
  } else {
    console.log(`Successfully created ${pageFiles.length} page files`);
  }

  return pageFiles;
}