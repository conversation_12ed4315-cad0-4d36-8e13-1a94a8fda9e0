import OpenAI from 'openai';
import { getRepository } from 'common-db-node/dist/db';
import { config } from '../config';
import { chatApiRequest } from './knowledge';
import { Mail } from '../../entity/email';
import { Project } from '../../entity/project';
import { GmailService, EmailMessage } from './gMail';

const openai = new OpenAI({ apiKey: config.openai.api_key });

export interface EmailProcessingResult {
  success: boolean;
  processedCount: number;
  failedCount: number;
  errors: string[];
}

export interface EmailProcessingOptions {
  maxEmails?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

interface EmailReplyOptions {
  originalEmail: EmailMessage;
  project: Project;
  companyInfo: {
    companyName?: string;
    companyAddress?: string;
    productPage?: string;
    contactUs?: string;
  };
}

interface EmailReplyResult {
  subject: string;
  textContent: string;
  htmlContent: string;
}

/**
 * 邮件自动回复服务
 * 集成了邮件处理和AI回复生成功能
 */
export class EmailAutoReplyService {
  private readonly emailRepository = getRepository(Mail);
  private readonly projectRepository = getRepository(Project);

  /**
   * 处理指定邮箱的未读邮件
   */
  async processUnreadEmails(
    emailRecord: Mail, 
    gmailService: GmailService, 
    options: EmailProcessingOptions = {}
  ): Promise<EmailProcessingResult> {
    const {
      maxEmails = 5,
      retryAttempts = 3,
      retryDelay = 1000,
    } = options;

    const result: EmailProcessingResult = {
      success: true,
      processedCount: 0,
      failedCount: 0,
      errors: [],
    };

    try {
      console.log(`🔍 开始处理未读邮件: ${emailRecord.email}`);

      // 1. 验证邮箱状态
      if (!this.validateEmailRecord(emailRecord)) {
        result.success = false;
        result.errors.push(`邮箱验证失败: ${emailRecord.email}`);
        return result;
      }

      // 2. 获取项目信息
      const project = await this.getProjectInfo(emailRecord.projectId);
      if (!project) {
        result.success = false;
        result.errors.push(`未找到项目: ${emailRecord.projectId}`);
        return result;
      }

      // 3. 获取未读邮件
      const unreadEmails = await this.getUnreadEmailsWithRetry(
        gmailService, 
        maxEmails, 
        retryAttempts, 
        retryDelay
      );

      console.log(`📬 发现 ${unreadEmails.length} 封未读邮件`);

      if (unreadEmails.length === 0) {
        console.log('✅ 没有未读邮件需要处理');
        return result;
      }

      // 4. 处理每封邮件
      for (const email of unreadEmails) {
        try {
          await this.processIndividualEmail(email, emailRecord, project, gmailService);
          result.processedCount++;
          console.log(`✅ 邮件处理成功: ${email.id}`);
        } catch (error) {
          result.failedCount++;
          const errorMsg = `处理邮件失败 ${email.id}: ${error instanceof Error ? error.message : '未知错误'}`;
          result.errors.push(errorMsg);
          console.error(`❌ ${errorMsg}`);
        }
      }

      result.success = result.failedCount === 0;
      console.log(`📊 邮件处理完成 - 成功: ${result.processedCount}, 失败: ${result.failedCount}`);

    } catch (error) {
      result.success = false;
      const errorMsg = `邮件处理流程失败: ${error instanceof Error ? error.message : '未知错误'}`;
      result.errors.push(errorMsg);
      console.error(`❌ ${errorMsg}`);
    }

    return result;
  }

  /**
   * 验证邮箱记录
   */
  private validateEmailRecord(emailRecord: Mail): boolean {
    if (!emailRecord.activated) {
      console.error(`邮箱未激活: ${emailRecord.email}`);
      return false;
    }

    if (!emailRecord.accessToken) {
      console.error(`邮箱未授权: ${emailRecord.email}`);
      return false;
    }

    if (emailRecord.expiresAt && emailRecord.expiresAt <= new Date()) {
      console.error(`访问令牌已过期: ${emailRecord.email}`);
      return false;
    }

    return true;
  }

  /**
   * 获取项目信息
   */
  private async getProjectInfo(projectId: string): Promise<Project | null> {
    try {
      return await this.projectRepository.findOne({
        where: { id: projectId },
      });
    } catch (error) {
      console.error(`获取项目信息失败: ${projectId}`, error);
      return null;
    }
  }

  /**
   * 带重试机制获取未读邮件
   */
  private async getUnreadEmailsWithRetry(
    gmailService: GmailService,
    maxEmails: number,
    retryAttempts: number,
    retryDelay: number
  ): Promise<EmailMessage[]> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= retryAttempts; attempt++) {
      try {
        return await gmailService.getUnreadEmails(maxEmails);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('未知错误');
        console.warn(`⚠️ 获取未读邮件失败 (尝试 ${attempt}/${retryAttempts}):`, lastError.message);
        
        if (attempt < retryAttempts) {
          await this.delay(retryDelay * attempt);
        }
      }
    }

    throw lastError || new Error('获取未读邮件失败');
  }

  /**
   * 处理单封邮件
   */
  private async processIndividualEmail(
    email: EmailMessage,
    emailRecord: Mail,
    project: Project,
    gmailService: GmailService
  ): Promise<void> {
    console.log(`📧 处理邮件: ${email.subject} (来自: ${email.from})`);

    // 1. 生成AI回复
    const replyResult = await this.generateEmailReply({
      originalEmail: email,
      project: project,
      companyInfo: {
        companyName: emailRecord.companyName,
        companyAddress: emailRecord.companyAddress,
        productPage: emailRecord.productPage,
        contactUs: emailRecord.contactUs,
      },
    });

    console.log(`🤖 AI生成回复完成: ${replyResult.subject}`);

    // 2. 发送回复邮件
    const sentMessageId = await gmailService.sendEmail({
      to: email.from,
      subject: replyResult.subject,
      text: replyResult.textContent,
      html: replyResult.htmlContent,
      threadId: email.threadId,
      replyToMessageId: email.id,
    });

    console.log(`📤 回复邮件已发送: ${sentMessageId}`);

    // 3. 标记原邮件为已读
    await gmailService.markAsRead(email.id);
    console.log(`✅ 邮件已标记为已读: ${email.id}`);
  }

  /**
   * 生成邮件回复内容
   */
  private async generateEmailReply(options: EmailReplyOptions): Promise<EmailReplyResult> {
    const { originalEmail, project, companyInfo } = options;
    
    try {
      // 1. 尝试使用知识库API生成回复
      let aiResponse = '';
      try {
        aiResponse = await this.generateReplyFromKnowledge(originalEmail, project);
      } catch (knowledgeError) {
        console.warn('知识库API调用失败，使用OpenAI备用方案:', knowledgeError);
        // 2. 使用OpenAI作为备用方案
        aiResponse = await this.generateReplyFromOpenAI(originalEmail, project, companyInfo);
      }

      // 3. 生成回复主题
      const replySubject = this.generateReplySubject(originalEmail.subject);

      // 4. 格式化回复内容
      const formattedReply = this.formatEmailReply(aiResponse, companyInfo);

      return {
        subject: replySubject,
        textContent: this.stripHtml(formattedReply),
        htmlContent: formattedReply,
      };

    } catch (error) {
      console.error('生成邮件回复失败:', error);
      throw new Error(`生成邮件回复失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 使用知识库API生成回复
   */
  private async generateReplyFromKnowledge(email: EmailMessage, project: Project): Promise<string> {
    const emailContent = email.body || email.snippet;
    const question = `客户发来邮件询问：

发件人：${email.from}
主题：${email.subject}
内容：${emailContent}

请根据我们的产品和服务信息，为这位客户提供专业、友好的回复。`;
    
    try {
      const response = await chatApiRequest(question, project.knowledgeId);
      
      return new Promise((resolve, reject) => {
        let answer = '';
        
        response.data.on('data', (chunk: any) => {
          const data = chunk.toString();
          try {
            const cleanData = data.startsWith('data:') ? data.slice(5) : data;
            const parsedData = JSON.parse(cleanData);
            if (parsedData && parsedData.data && parsedData.data.answer) {
              answer = parsedData.data.answer.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
            }
          } catch (parseError) {
            // 忽略解析错误，继续处理
          }
        });

        response.data.on('end', () => {
          if (answer) {
            resolve(answer);
          } else {
            reject(new Error('知识库API未返回有效回复'));
          }
        });

        response.data.on('error', (error: any) => {
          reject(error);
        });
      });
    } catch (error) {
      throw new Error(`知识库API调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 使用OpenAI生成回复（备用方案）
   */
  private async generateReplyFromOpenAI(
    email: EmailMessage, 
    project: Project, 
    companyInfo: EmailReplyOptions['companyInfo']
  ): Promise<string> {
    const customPrompt = project.settings?.customPrompt || '';
    const emailContent = email.body || email.snippet;
    
    const prompt = `你是${companyInfo.companyName || project.name}的客服代表。请为以下客户邮件生成一个专业、友好的回复。

公司信息：
- 公司名称：${companyInfo.companyName || project.name}
- 网站：${project.website}
- 产品页面：${companyInfo.productPage || ''}
- 联系方式：${companyInfo.contactUs || ''}
- 公司地址：${companyInfo.companyAddress || ''}

${customPrompt ? `特殊指示：${customPrompt}` : ''}

客户邮件：
发件人：${email.from}
主题：${email.subject}
内容：${emailContent}

请生成一个回复，要求：
1. 语气专业友好
2. 直接回答客户的问题
3. 如果需要更多信息，礼貌地询问
4. 包含适当的联系方式
5. 使用HTML格式，包含适当的段落和格式

回复内容：`;
    
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 1000,
      });

      const reply = response.choices[0]?.message?.content?.trim();
      if (!reply) {
        throw new Error('OpenAI未返回有效回复');
      }

      return reply;
    } catch (error) {
      throw new Error(`OpenAI API调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 生成回复主题
   */
  private generateReplySubject(originalSubject: string): string {
    if (!originalSubject) {
      return 'Re: Your Inquiry';
    }

    const cleanSubject = originalSubject.trim();
    if (cleanSubject.toLowerCase().startsWith('re:')) {
      return cleanSubject;
    }

    return `Re: ${cleanSubject}`;
  }

  /**
   * 格式化邮件回复内容
   */
  private formatEmailReply(content: string, companyInfo: EmailReplyOptions['companyInfo']): string {
    // 如果内容已经是HTML格式，直接使用
    if (content.includes('<') && content.includes('>')) {
      return this.addEmailSignature(content, companyInfo);
    }

    // 将纯文本转换为HTML格式
    const htmlContent = content
      .split('\n\n')
      .map(paragraph => `<p>${paragraph.replace(/\n/g, '<br>')}</p>`)
      .join('\n');

    return this.addEmailSignature(htmlContent, companyInfo);
  }

  /**
   * 添加邮件签名
   */
  private addEmailSignature(content: string, companyInfo: EmailReplyOptions['companyInfo']): string {
    const signature = `
<br><br>
<div style="border-top: 1px solid #ccc; padding-top: 10px; margin-top: 20px;">
  <p><strong>Best regards,</strong><br>
  ${companyInfo.companyName || 'Customer Service Team'}</p>
  ${companyInfo.contactUs ? `<p>Contact: ${companyInfo.contactUs}</p>` : ''}
  ${companyInfo.productPage ? `<p>Website: <a href="${companyInfo.productPage}">${companyInfo.productPage}</a></p>` : ''}
</div>`;

    return content + signature;
  }

  /**
   * 移除HTML标签，获取纯文本内容
   */
  private stripHtml(html: string): string {
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .trim();
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 创建邮件自动回复服务实例
 */
export function createEmailAutoReplyService(): EmailAutoReplyService {
  return new EmailAutoReplyService();
}

// 导出默认实例创建函数
export default createEmailAutoReplyService;
