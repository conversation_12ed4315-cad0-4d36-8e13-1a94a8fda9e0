import MarkdownIt from 'markdown-it';
import createDOMPurify, { type WindowLike } from 'dompurify';
import { JSDOM } from 'jsdom';

export function markdownToHtml(md: string): string {
  const mdParser = new MarkdownIt({
    html: false,
    linkify: true,
    breaks: true,
  });

  const rawHtml = mdParser.render(md ?? '');

  const { window } = new JSDOM('');
  const DOMPurify = createDOMPurify(window as unknown as WindowLike);

  return DOMPurify.sanitize(rawHtml);
}
