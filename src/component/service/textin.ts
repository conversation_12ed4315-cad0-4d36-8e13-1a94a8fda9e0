import axios, { AxiosResponse } from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import { config } from '../config';

interface OCROptions {
    dpi?: number;
    get_image?: string;
    markdown_details?: number;
    page_count?: number;
    parse_mode?: string;
    table_flavor?: string;
    [key: string]: string | number | undefined;
}

interface OCRResult {
    result?: {
        markdown?: string;
        [key: string]: any;
    };
    [key: string]: any;
}

export interface ConvertOptions {
    inputPath: string;
    outputPath?: string;
    saveJson?: boolean;
    ocrOptions?: OCROptions;
}

/**
 * 将PDF文件转换为Markdown格式
 * @param options 转换配置选项
 * @returns Promise<string> 返回markdown内容
 */
export async function convertPdfToMarkdown(options: ConvertOptions): Promise<string> {
  const {
    inputPath,
    outputPath,
    saveJson = false,
    ocrOptions = {},
  } = options;

  // 默认OCR选项
  const defaultOcrOptions: OCROptions = {
    dpi: 144,
    get_image: 'objects',
    markdown_details: 1,
    page_count: 10,
    parse_mode: 'auto',
    table_flavor: 'html',
  };

  const finalOcrOptions = { ...defaultOcrOptions, ...ocrOptions };

  try {
    // 读取PDF文件
    const fileContent: Buffer = fs.readFileSync(path.resolve(inputPath));

    // 构建请求参数
    const params: Record<string, string> = {};
    for (const key in finalOcrOptions) {
      if (finalOcrOptions[key] !== undefined) {
        params[key] = String(finalOcrOptions[key]);
      }
    }

    // 设置请求头
    const headers = {
      'x-ti-app-id': config.textIn.appId,
      'x-ti-secret-code': config.textIn.secretCode,
      'Content-Type': 'application/octet-stream',
    };

    // 发送OCR请求
    const response: AxiosResponse<string> = await axios.post(config.textIn.url, fileContent,
      {
        headers,
        params,
        responseType: 'text',
      }
    );

    // 解析响应
    const json: OCRResult = JSON.parse(response.data);

    // 保存JSON响应（可选）
    if (saveJson && outputPath) {
      const jsonPath = outputPath.replace(/\.txt$/, '.json');
      fs.writeFileSync(jsonPath, response.data, 'utf8');
    }

    // 提取markdown内容
    const markdownContent = json.result?.markdown || '';

    // 保存markdown文件（如果指定了输出路径）
    if (outputPath && markdownContent) {
      fs.writeFileSync(outputPath, markdownContent, 'utf8');
    }

    return markdownContent;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`PDF to Markdown conversion failed: ${errorMessage}`);
  }
}