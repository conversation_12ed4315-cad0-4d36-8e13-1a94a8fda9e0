import { ChunkMethod, FileUploadResponse, KnowledgeApiResponse, ResponseCode } from '../types';
import { config } from '../config';
import axios from 'axios';
import FormData from 'form-data';
import * as fs from 'fs';
import { badRequest } from '../http';

export async function createKnowledge(knowledgeName: string, description: string): Promise<KnowledgeApiResponse> {
  const timestamp = Date.now();
  const uniqueKnowledgeName = `${knowledgeName}_${timestamp}`;

  return (await axios.post(config.knowledgeBase.dataset, {
    name: uniqueKnowledgeName,
    description: description,
    /*parser_config: {
      chunk_token_num: 256,
      delimiter: '.`?`。',
      filename_embd_weight:0.1,
    },*/
  }, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${String(config.knowledgeBase.api_key)}`,
    },
  })).data;
}

export async function deleteKnowledge(knowledgeId: string): Promise<number> {
  return (await axios.delete(config.knowledgeBase.dataset, {
    data: {
      ids: [knowledgeId.replace(/-/g, '')],
    },
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${String(config.knowledgeBase.api_key)}`,
    },
  })).data.code;
}

export async function addKnowledgeDocs(knowledgeId: string, filePath: string, fileName: string, mimeType: string): Promise<FileUploadResponse> {
  const formData = new FormData();
  formData.append('file', fs.createReadStream(filePath), {
    filename: fileName,
    contentType: mimeType,
  });

  const requestUrl = config.knowledgeBase.dataset + '/' + knowledgeId.replace(/-/g, '') + '/documents';
  const response = await axios.post(requestUrl, formData, {
    headers: {
      'Content-Type': `multipart/form-data; boundary=${formData.getBoundary()}`,
      'Authorization': `Bearer ${String(config.knowledgeBase.api_key)}`,
    },
    maxBodyLength: Infinity,
    maxContentLength: Infinity,
    maxRedirects: 0,
  });
  //console.log('upload knowledge docs: ', fileName, filePath, mimeType, response.data);
  return response.data;
}

export async function updateKnowledgeDocs(knowledgeId: string, fileId: string, chunkMethod: ChunkMethod) {
  const requestUrl = config.knowledgeBase.dataset + '/' + knowledgeId.replace(/-/g, '') + '/documents/' + fileId;
  return (await axios.put(requestUrl, {
    chunk_method: chunkMethod,
  }, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${String(config.knowledgeBase.api_key)}`,
    },
  })).data;
}

export async function parseKnowledgeDocs(knowledgeId: string, fileId: string): Promise<FileUploadResponse> {
  const requestUrl = config.knowledgeBase.dataset + '/' + knowledgeId.replace(/-/g, '') + '/chunks';
  return (await axios.post(requestUrl, {
    document_ids: [fileId],
  }, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${String(config.knowledgeBase.api_key)}`,
    },
  })).data;
}

export async function deleteKnowledgeDocs(knowledgeId: string, fileId: string): Promise<number> {
  const requestUrl = config.knowledgeBase.dataset + '/' + knowledgeId.replace(/-/g, '') + '/documents';
  return (await axios.delete(requestUrl, {
    data: {
      ids: [fileId.replace(/-/g, '')],
    },
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${String(config.knowledgeBase.api_key)}`,
    },
  })).data.code;
}

export async function createAgentSession(knowledgeId: string, formUrl?: string, whatsappUrl?: string): Promise<string> {
  const requestBody = {
    knowledgebase_id: knowledgeId.replace(/-/g, ''),
    form_url: formUrl ?? 'empty',
    whatsapp_url: whatsappUrl ?? 'empty',
  };
  const requestUrl = String(config.knowledgeBase.agent_url) + String(config.knowledgeBase.agent_id) + '/sessions';
  const response = (await axios.post(requestUrl, requestBody, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${String(config.knowledgeBase.api_key)}`,
    },
  })).data;
  if (response.code !== 0) {
    console.log('create agent session error: ', response);
    throw badRequest(ResponseCode.create_session_failed);
  }
  console.log('create agent session: ', response.data.id);
  return response.data.id;
}

export async function chatApiRequest(question: string, knowledgeId: string, sessionId?: string) {
  const requestUrl = String(config.knowledgeBase.agent_url) + config.knowledgeBase.agent_id + '/completions';
  const requestBody = {
    question: question,
    dataset_ids: knowledgeId.replace(/-/g, ''),
    stream: true,
    session_id: sessionId?.replace(/-/g, ''),
  };

  return await axios.post(requestUrl, requestBody, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${String(config.knowledgeBase.api_key)}`,
    },
    responseType: 'stream',
    timeout: 60000,
  });
}