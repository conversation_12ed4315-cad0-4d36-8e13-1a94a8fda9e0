import { getRepository } from 'common-db-node/dist/db';
import { Mail } from '../../entity/email';
import { Project } from '../../entity/project';
import { GmailService, EmailMessage } from './gMail';
import { createAIEmailReplyService, AIEmailReplyService } from './aiEmailReply';

export interface EmailProcessingResult {
  success: boolean;
  processedCount: number;
  failedCount: number;
  errors: string[];
}

export interface EmailProcessingOptions {
  maxEmails?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

export class EmailProcessorService {
  private readonly emailRepository = getRepository(Mail);
  private readonly projectRepository = getRepository(Project);
  private readonly aiReplyService: AIEmailReplyService;

  constructor() {
    this.aiReplyService = createAIEmailReplyService();
  }

  /**
   * 处理指定邮箱的未读邮件
   */
  async processUnreadEmails(
    emailRecord: Mail, 
    gmailService: GmailService, 
    options: EmailProcessingOptions = {}
  ): Promise<EmailProcessingResult> {
    const {
      maxEmails = 5,
      retryAttempts = 3,
      retryDelay = 1000,
    } = options;

    const result: EmailProcessingResult = {
      success: true,
      processedCount: 0,
      failedCount: 0,
      errors: [],
    };

    try {
      console.log(`🔍 开始处理未读邮件: ${emailRecord.email}`);

      // 1. 验证邮箱状态
      const validationResult = await this.validateEmailRecord(emailRecord);
      if (!validationResult.isValid) {
        result.success = false;
        result.errors.push(validationResult.error!);
        return result;
      }

      // 2. 获取项目信息
      const project = await this.getProjectInfo(emailRecord.projectId);
      if (!project) {
        result.success = false;
        result.errors.push(`未找到项目: ${emailRecord.projectId}`);
        return result;
      }

      // 3. 获取未读邮件
      const unreadEmails = await this.getUnreadEmailsWithRetry(
        gmailService, 
        maxEmails, 
        retryAttempts, 
        retryDelay
      );

      console.log(`📬 发现 ${unreadEmails.length} 封未读邮件`);

      if (unreadEmails.length === 0) {
        console.log('✅ 没有未读邮件需要处理');
        return result;
      }

      // 4. 处理每封邮件
      for (const email of unreadEmails) {
        try {
          await this.processIndividualEmailWithRetry(
            email,
            emailRecord,
            project,
            gmailService,
            retryAttempts,
            retryDelay
          );
          result.processedCount++;
          console.log(`✅ 邮件处理成功: ${email.id}`);
        } catch (error) {
          result.failedCount++;
          const errorMsg = `处理邮件失败 ${email.id}: ${error instanceof Error ? error.message : '未知错误'}`;
          result.errors.push(errorMsg);
          console.error(`❌ ${errorMsg}`);
        }
      }

      // 5. 更新最终结果
      result.success = result.failedCount === 0;
      console.log(`📊 邮件处理完成 - 成功: ${result.processedCount}, 失败: ${result.failedCount}`);

    } catch (error) {
      result.success = false;
      const errorMsg = `邮件处理流程失败: ${error instanceof Error ? error.message : '未知错误'}`;
      result.errors.push(errorMsg);
      console.error(`❌ ${errorMsg}`);
    }

    return result;
  }

  private async validateEmailRecord(emailRecord: Mail): Promise<{ isValid: boolean; error?: string }> {
    if (!emailRecord.activated) {
      return { isValid: false, error: `邮箱未激活: ${emailRecord.email}` };
    }

    if (!emailRecord.accessToken) {
      return { isValid: false, error: `邮箱未授权: ${emailRecord.email}` };
    }

    // 检查令牌是否过期
    if (emailRecord.expiresAt && emailRecord.expiresAt <= new Date()) {
      return { isValid: false, error: `访问令牌已过期: ${emailRecord.email}` };
    }

    return { isValid: true };
  }

  private async getProjectInfo(projectId: string): Promise<Project | null> {
    try {
      return await this.projectRepository.findOne({
        where: { id: projectId },
      });
    } catch (error) {
      console.error(`获取项目信息失败: ${projectId}`, error);
      return null;
    }
  }

  private async getUnreadEmailsWithRetry(
    gmailService: GmailService,
    maxEmails: number,
    retryAttempts: number,
    retryDelay: number
  ): Promise<EmailMessage[]> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= retryAttempts; attempt++) {
      try {
        return await gmailService.getUnreadEmails(maxEmails);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('未知错误');
        console.warn(`⚠️ 获取未读邮件失败 (尝试 ${attempt}/${retryAttempts}):`, lastError.message);
        
        if (attempt < retryAttempts) {
          await this.delay(retryDelay * attempt); // 递增延迟
        }
      }
    }

    throw lastError || new Error('获取未读邮件失败');
  }

  private async processIndividualEmailWithRetry(
    email: EmailMessage,
    emailRecord: Mail,
    project: Project,
    gmailService: GmailService,
    retryAttempts: number,
    retryDelay: number
  ): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= retryAttempts; attempt++) {
      try {
        await this.processIndividualEmail(email, emailRecord, project, gmailService);
        return; // 成功则返回
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('未知错误');
        console.warn(`⚠️ 处理邮件失败 ${email.id} (尝试 ${attempt}/${retryAttempts}):`, lastError.message);
        
        if (attempt < retryAttempts) {
          await this.delay(retryDelay * attempt);
        }
      }
    }

    throw lastError || new Error('处理邮件失败');
  }

  private async processIndividualEmail(
    email: EmailMessage,
    emailRecord: Mail,
    project: Project,
    gmailService: GmailService
  ): Promise<void> {
    console.log(`📧 处理邮件: ${email.subject} (来自: ${email.from})`);

    // 1. 生成AI回复
    const replyResult = await this.aiReplyService.generateEmailReply({
      originalEmail: email,
      project: project,
      companyInfo: {
        companyName: emailRecord.companyName,
        companyAddress: emailRecord.companyAddress,
        productPage: emailRecord.productPage,
        contactUs: emailRecord.contactUs,
      },
    });

    console.log(`🤖 AI生成回复完成: ${replyResult.subject}`);

    // 2. 发送回复邮件
    const sentMessageId = await gmailService.sendEmail({
      to: email.from,
      subject: replyResult.subject,
      text: replyResult.textContent,
      html: replyResult.htmlContent,
      threadId: email.threadId,
      replyToMessageId: email.id,
    });

    console.log(`📤 回复邮件已发送: ${sentMessageId}`);

    // 3. 标记原邮件为已读
    await gmailService.markAsRead(email.id);
    console.log(`✅ 邮件已标记为已读: ${email.id}`);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export function createEmailProcessorService(): EmailProcessorService {
  return new EmailProcessorService();
}

// 导出默认实例创建函数
export default createEmailProcessorService;
