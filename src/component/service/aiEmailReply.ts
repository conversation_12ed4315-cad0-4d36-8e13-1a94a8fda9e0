import OpenAI from 'openai';
import { config } from '../config';
import { chatApiRequest } from './knowledge';
import { EmailMessage } from './gMail';
import { Project } from '../../entity/project';

const openai = new OpenAI({ apiKey: config.openai.api_key });

export interface EmailReplyOptions {
  originalEmail: EmailMessage;
  project: Project;
  companyInfo: {
    companyName?: string;
    companyAddress?: string;
    productPage?: string;
    contactUs?: string;
  };
}

export interface EmailReplyResult {
  subject: string;
  textContent: string;
  htmlContent: string;
}

export class AIEmailReplyService {
  async generateEmailReply(options: EmailReplyOptions): Promise<EmailReplyResult> {
    const { originalEmail, project, companyInfo } = options;
    
    try {
      // 1. 首先尝试使用知识库API生成回复
      let aiResponse = '';
      try {
        const knowledgeResponse = await this.generateReplyFromKnowledge(originalEmail, project);
        aiResponse = knowledgeResponse;
      } catch (knowledgeError) {
        console.warn('知识库API调用失败，使用OpenAI备用方案:', knowledgeError);
        // 2. 如果知识库API失败，使用OpenAI作为备用方案
        aiResponse = await this.generateReplyFromOpenAI(originalEmail, project, companyInfo);
      }

      // 3. 生成回复主题
      const replySubject = this.generateReplySubject(originalEmail.subject);

      // 4. 格式化回复内容
      const formattedReply = this.formatEmailReply(aiResponse, companyInfo);

      return {
        subject: replySubject,
        textContent: this.stripHtml(formattedReply),
        htmlContent: formattedReply,
      };

    } catch (error) {
      console.error('生成邮件回复失败:', error);
      throw new Error(`生成邮件回复失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  private async generateReplyFromKnowledge(email: EmailMessage, project: Project): Promise<string> {
    const question = this.constructQuestionFromEmail(email);
    
    try {
      const response = await chatApiRequest(question, project.knowledgeId);
      
      return new Promise((resolve, reject) => {
        let answer = '';
        
        response.data.on('data', (chunk: any) => {
          const data = chunk.toString();
          try {
            const cleanData = data.startsWith('data:') ? data.slice(5) : data;
            const parsedData = JSON.parse(cleanData);
            if (parsedData && parsedData.data && parsedData.data.answer) {
              answer = parsedData.data.answer.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
            }
          } catch (parseError) {
            // 忽略解析错误，继续处理
          }
        });

        response.data.on('end', () => {
          if (answer) {
            resolve(answer);
          } else {
            reject(new Error('知识库API未返回有效回复'));
          }
        });

        response.data.on('error', (error: any) => {
          reject(error);
        });
      });
    } catch (error) {
      throw new Error(`知识库API调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  private async generateReplyFromOpenAI(
    email: EmailMessage, 
    project: Project, 
    companyInfo: EmailReplyOptions['companyInfo']
  ): Promise<string> {
    const prompt = this.constructOpenAIPrompt(email, project, companyInfo);
    
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 1000,
      });

      const reply = response.choices[0]?.message?.content?.trim();
      if (!reply) {
        throw new Error('OpenAI未返回有效回复');
      }

      return reply;
    } catch (error) {
      throw new Error(`OpenAI API调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  private constructQuestionFromEmail(email: EmailMessage): string {
    const emailContent = email.body || email.snippet;
    return `客户发来邮件询问：

发件人：${email.from}
主题：${email.subject}
内容：${emailContent}

请根据我们的产品和服务信息，为这位客户提供专业、友好的回复。`;
  }

  private constructOpenAIPrompt(
    email: EmailMessage, 
    project: Project, 
    companyInfo: EmailReplyOptions['companyInfo']
  ): string {
    const customPrompt = project.settings?.customPrompt || '';
    const emailContent = email.body || email.snippet;
    
    return `你是${companyInfo.companyName || project.name}的客服代表。请为以下客户邮件生成一个专业、友好的回复。

公司信息：
- 公司名称：${companyInfo.companyName || project.name}
- 网站：${project.website}
- 产品页面：${companyInfo.productPage || ''}
- 联系方式：${companyInfo.contactUs || ''}
- 公司地址：${companyInfo.companyAddress || ''}

${customPrompt ? `特殊指示：${customPrompt}` : ''}

客户邮件：
发件人：${email.from}
主题：${email.subject}
内容：${emailContent}

请生成一个回复，要求：
1. 语气专业友好
2. 直接回答客户的问题
3. 如果需要更多信息，礼貌地询问
4. 包含适当的联系方式
5. 使用HTML格式，包含适当的段落和格式

回复内容：`;
  }

  private generateReplySubject(originalSubject: string): string {
    if (!originalSubject) {
      return 'Re: Your Inquiry';
    }
    
    const cleanSubject = originalSubject.trim();
    if (cleanSubject.toLowerCase().startsWith('re:')) {
      return cleanSubject;
    }
    
    return `Re: ${cleanSubject}`;
  }

  private formatEmailReply(content: string, companyInfo: EmailReplyOptions['companyInfo']): string {
    // 如果内容已经是HTML格式，直接使用
    if (content.includes('<') && content.includes('>')) {
      return this.addEmailSignature(content, companyInfo);
    }

    // 将纯文本转换为HTML格式
    const htmlContent = content
      .split('\n\n')
      .map(paragraph => `<p>${paragraph.replace(/\n/g, '<br>')}</p>`)
      .join('\n');

    return this.addEmailSignature(htmlContent, companyInfo);
  }

  private addEmailSignature(content: string, companyInfo: EmailReplyOptions['companyInfo']): string {
    const signature = `
<br><br>
<div style="border-top: 1px solid #ccc; padding-top: 10px; margin-top: 20px;">
  <p><strong>Best regards,</strong><br>
  ${companyInfo.companyName || 'Customer Service Team'}</p>
  ${companyInfo.contactUs ? `<p>Contact: ${companyInfo.contactUs}</p>` : ''}
  ${companyInfo.productPage ? `<p>Website: <a href="${companyInfo.productPage}">${companyInfo.productPage}</a></p>` : ''}
</div>`;

    return content + signature;
  }

  private stripHtml(html: string): string {
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .trim();
  }
}

export function createAIEmailReplyService(): AIEmailReplyService {
  return new AIEmailReplyService();
}

export default createAIEmailReplyService;
