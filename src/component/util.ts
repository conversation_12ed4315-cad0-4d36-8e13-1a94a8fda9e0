import * as crypto from 'crypto';
import * as config from '../resource/config.json';
import * as jwt from 'jsonwebtoken';
import { User } from '../entity/user';
import { privateKey } from './config';
import { ADMIN_USER, AuthorizedUser, isAdmin, isRoot, isSuper, isTenantAdmin, NORMAL_USER, SUPER_ADMIN, TENANT_ADMIN } from './http';
import { JwtPayload } from './http';
import * as os from 'os';
import { randomUUID } from 'crypto';
import * as path from 'path';
import * as fs from 'fs';
import axios from 'axios';

export function generateToken(user: User): string {
  const userId = user.id;
  const expireIn = config.settings.expireIn;
  const roles = user.roles.map(r => r.name);
  const payload: JwtPayload = {
    sub: userId,
    name: user.name,
    exp: Math.trunc(Date.now() / 1000) + expireIn,
    roles: roles,
  };
  return jwt.sign(payload, privateKey, { algorithm: 'RS256' });
}

export async function asyncFilter<T>(array: T[], filter: (item: T) => Promise<boolean>): Promise<T[]> {
  const results = await Promise.all(array.map(filter));
  return array.filter((item, index) => results[index]);
}

export async function verify(password: string, hash: string): Promise<boolean> {
  if (!password) {
    return !hash;
  }
  return new Promise((resolve, reject) => {
    const [salt, key] = hash.split(':');
    crypto.scrypt(password, salt, 64, (err, derivedKey) => {
      if (err) reject(err);
      resolve(key === derivedKey.toString('hex'));
    });
  });
}

export async function hash(password: string): Promise<string> {
  return new Promise<string>((resolve, reject) => {
    // generate random 16 bytes long salt
    const salt = crypto.randomBytes(16).toString('hex');
    crypto.scrypt(password, salt, 64, (err, derivedKey) => {
      if (err) reject(err);
      resolve(salt + ':' + derivedKey.toString('hex'));
    });
  });
}

const PASSWORD_CHARS = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
export function randomPassword(length: number): string {
  let result = '';
  for (let i = 0; i < length; i++ ) {
    result += PASSWORD_CHARS.charAt(Math.trunc(Math.random() * PASSWORD_CHARS.length));
  }
  return result;
}

export function toArray(str: string): string[] {
  return str.split(',').map(str => str.trim());
}

export function validateRole(user: AuthorizedUser, roles: string[]): boolean {
  if (isRoot(user)) {
    return true;
  } else if (isSuper(user)) {
    return roles.every(role => [SUPER_ADMIN, TENANT_ADMIN, ADMIN_USER, NORMAL_USER].includes(role));
  } else if (isTenantAdmin(user)) {
    return roles.every(role => [TENANT_ADMIN, ADMIN_USER, NORMAL_USER].includes(role));
  } else if (isAdmin(user)) {
    return roles.every(role => [ADMIN_USER, NORMAL_USER].includes(role));
  } else {
    return false;
  }
}

export function isPagination(query: { pageNumber?: string; pageSize?: string; }): query is { pageNumber: string; pageSize: string; } {
  return !!(query.pageNumber && query.pageSize);
}

export function paginate(pageNumber: string, pageSize: string): { take: number; skip: number } {
  return { take: Number(pageSize), skip : (Number(pageNumber) - 1) * Number(pageSize) };
}

export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export function notNull<T>(value: T | null | undefined): T {
  if (value === null || value === undefined) {
    throw new Error(`value should not be empty`);
  }
  return value;
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
}

export async function downloadFile(url: string, destination?: string): Promise<string> {
  const tmpDir = os.tmpdir();
  const filename = randomUUID() + path.parse(url).ext;
  const localFilePath = destination || path.join(tmpDir, filename);

  const response = await axios({
    url,
    method: 'GET',
    responseType: 'stream',
  });

  const writer = fs.createWriteStream(localFilePath);
  response.data.pipe(writer);

  return new Promise((resolve, reject) => {
    writer.on('finish', () => resolve(localFilePath));
    writer.on('error', reject);
  });
}

export function generateSmsCode(): string {
  const digits = '0123456789'; // 定义所有可能的数字字符
  let code = '';

  for (let i = 0; i < 6; i++) {
    const randomIndex = Math.floor(Math.random() * digits.length); // 生成随机索引
    code += digits[randomIndex]; // 从可能的数字字符中选取一个添加到验证码字符串
  }

  return code;
}

export function getFileExtension(fileName: string): string {
  const index = fileName.lastIndexOf('.');
  return index > -1 ? fileName.substring(index) : '';
}

export function createCacheDir(targetPath: string): void {
  if (!fs.existsSync(targetPath)) {
    fs.mkdirSync(targetPath, { recursive: true });
  }
}

export function deleteLocalFile(filePath: string): Promise<void> {
  return new Promise((resolve) => {
    const absolutePath = path.resolve(filePath);

    fs.access(absolutePath, fs.constants.F_OK, (err) => {
      if (err) {
        console.log(`[deleteLocalFile]文件不存在: ${absolutePath}`);
        resolve();
        return;
      }

      fs.unlink(absolutePath, (unlinkErr) => {
        if (unlinkErr) {
          console.error(`[deleteLocalFile]删除文件失败: ${unlinkErr.message}`);
        } else {
          console.log(`[deleteLocalFile]文件删除成功: ${absolutePath}`);
        }
        resolve();
      });
    });
  });
}

export function getCurrentTime(): string {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-
      ${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}-
      ${String(now.getMinutes()).padStart(2, '0')}-${String(now.getSeconds()).padStart(2, '0')}`;
}

export function isNull<T>(value: T | null | undefined): boolean {
  return value === null || value === undefined || value === '';
}