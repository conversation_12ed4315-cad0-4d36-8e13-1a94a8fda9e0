import * as path from 'path';
import { DbOptions } from 'common-db-node/dist/db';
import { APPLICATION_NAME, LOG_LEVEL, PORT } from './constant';
import * as config from '../resource/config.json';
import * as fs from 'fs';
import { HttpServerOptions } from './http';

export const dbOptions: DbOptions = {
  url: config.db.url,
  schema: config.db.schema,
  logging: LOG_LEVEL === 'debug',
  entities: [path.resolve(__dirname, '../entity/*.js')],
  migrations: [path.resolve(__dirname, '../migration/*.js')],
};

export const privateKey = fs.readFileSync(path.resolve(__dirname, '../resource/private.key'), 'utf8');
export const publicKey = fs.readFileSync(path.resolve(__dirname, '../resource/public.pem'), 'utf8');
export async function httpOptions(): Promise<HttpServerOptions> {
  return {
    port: Number(PORT),
    controllers: [path.resolve(__dirname, `../controller/*.js`)],
    publicKey: publicKey,
    application: APPLICATION_NAME,
  };
}

export { config };
