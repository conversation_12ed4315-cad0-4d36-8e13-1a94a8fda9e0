import * as cron from 'node-cron';
import { getRepository } from 'common-db-node/dist/db';
import { IsNull, LessThan } from 'typeorm';
import { Session } from '../entity/session';
import { Chat } from '../entity/chat';
import OpenAI from 'openai';
import { config } from './config';
import { sendMail, SendMailOptions } from './service/aliyunEmail';
import { Project } from '../entity/project';
import { markdownToHtml } from './service/markdownToHtml';
import { Mail } from '../entity/email';

const openai = new OpenAI({ apiKey: config.openai.api_key });

export class Execution {
  private readonly sessionRepository = getRepository(Session);
  private readonly chatRepository = getRepository(Chat);
  private readonly projectRepository = getRepository(Project);
  private readonly emailRepository = getRepository(Mail);

  private async execution(): Promise<void> {
    await this.processSummary();
    await this.processEmail();
  }

  private async processEmail() {
    const emails = await this.emailRepository.find({
      where: {
        activated: true,
        authorized: true,
      },
    });

    // todo
  }

  private async processSummary() {
    //check session to call summary api
    const sessions = await this.sessionRepository.find({
      where: {
        summary: IsNull(),
        updateTime: LessThan(new Date(Date.now() - 5 * 60 * 1000)),
      },
    });

    // call summary api
    for (const session of sessions) {
      // 1. 查找当前 session 的所有聊天记录
      const chats = await this.chatRepository.find({
        where: { sessionId: session.id },
        order: { createTime: 'ASC' },
      });

      if (!chats.length) continue;

      // 2. 查找Project的Notification设置
      const project = await this.projectRepository.findOneOrFail({
        where: { id: session.projectId },
        relations: ['user'],
      });
      if (!project.settings.notificationEnabled) continue;

      const emailList: string[] = project.settings.notificationEmails || [project.user.email];

      // 3. 拼接对话内容
      const conversationText = chats.map(
        (chat) =>
          `Q: ${chat.question?.trim()}\nA: ${chat.answer?.trim()}`
      ).join('\n\n');

      try {
        // 4. 调用 OpenAI 生成总结
        const prompt = project.settings.customPrompt || `You are an AI assistant that creates warm, concise summaries of customer conversations for business owners. 
        Please write the summary in short, natural sentences that sound like a helpful update rather than a formal report.
        Instructions:
        Keep it friendly, clear, and professional.
        Focus on what the customer asked, how the agent responded, and what still needs attention.
        Mention if follow-up is needed, but in a supportive tone.
        Include overall customer mood in a natural way.
        Output Style:
        A short email-style paragraph (5–7 sentences), warm and easy to read, not bullet points.
        **Conversation History：\n\n${conversationText}`;

        const response = await openai.chat.completions.create({
          model: 'gpt-4o-mini',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.3,
        });

        const summary = response.choices[0]?.message?.content?.trim() || '';
        if (summary) {
          // 5. 更新 session 的 summary 字段
          session.summary = summary;
          await this.sessionRepository.save(session);

          // 6. 发送邮件通知
          const htmlSummary = markdownToHtml(summary);
          const formatDate = (date: Date) => {
            return new Date(date).toLocaleString('en-US', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' });
          };

          for (const email of emailList) {
            const htmlBody = `
                <!DOCTYPE html>
                <html>
                <head>
                  <meta charset="utf-8">
                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                  <title>Conversation Summary</title>
                  <style>
                    body {
                      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Roboto, Arial, sans-serif;
                      line-height: 1.6;
                      color: #333;
                      max-width: 600px;
                      margin: 0 auto;
                      padding: 20px;
                      background-color: #f8f9fa;
                    }
                    .container {
                      background: white;
                      border-radius: 8px;
                      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                      overflow: hidden;
                    }
                    .header {
                      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                      color: white;
                      padding: 30px 20px;
                      text-align: center;
                    }
                    .header h1 {
                      margin: 0;
                      font-size: 24px;
                      font-weight: 600;
                    }
                    .content {
                      padding: 30px 20px;
                    }
                    .summary-box {
                      background: #f8f9ff;
                      border-left: 4px solid #667eea;
                      padding: 20px;
                      margin: 20px 0;
                      border-radius: 0 6px 6px 0;
                    }
                    .summary-text {
                      font-size: 16px;
                      line-height: 1.8;
                      color: #444;
                      margin: 0;
                    }
                    .meta-info {
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                      margin-top: 25px;
                      padding-top: 20px;
                      border-top: 1px solid #eee;
                      font-size: 14px;
                      color: #666;
                    }
                    .project-name {
                      font-weight: 600;
                      color: #667eea;
                    }
                    .footer {
                      background: #f8f9fa;
                      padding: 20px;
                      text-align: center;
                      font-size: 12px;
                      color: #999;
                      border-top: 1px solid #eee;
                    }
                    @media (max-width: 480px) {
                      body { padding: 10px; }
                      .content { padding: 20px 15px; }
                      .meta-info { flex-direction: column; gap: 10px; }
                    }
                  </style>
                </head>
                <body>
                  <div class="container">
                    <div class="header">
                      <h1>📋 Conversation Summary</h1>
                    </div>
                    <div class="content">
                      <p>Hi ${email.split('@')[0]},</p>
                      <p>Your conversation summary has been generated:</p>
                      
                      <div class="summary-box">
                        <p class="summary-text">${htmlSummary}</p>
                      </div>
                      
                      <div class="meta-info">
                        <div class="project-name">Project: ${project.name || 'Unknown'}</div>
                        <div>Generated: ${formatDate(new Date())}</div>
                      </div>
                      
                      <p style="margin-top: 25px; color: #666;">
                        If you have any questions, please feel free to contact us.
                      </p>
                    </div>
                    <div class="footer">
                      <p>This email is sent automatically by the system. Please do not reply.</p>
                    </div>
                  </div>
                </body>
                </html>`;
            const params: SendMailOptions = {
              to: email,
              subject: 'Conversation Summary - Generated Successfully',
              htmlBody: htmlBody,
              tagName: 'Conversation Summary',
            };
            await sendMail(params);
            console.log(`✅ Summary generated and email notification sent: ${session.id}, ${email}, ${summary}`);
          }
        } else {
          console.warn(`⚠️ Unable to generate summary: ${session.id}`);
        }
      } catch (err) {
        console.error(`❌ 调用 OpenAI 出错: ${session.id}`, err);
      }
    }
  }

  scheduleTask() {
    cron.schedule('*/1 * * * *', async () => {
      console.log('***** start Schedule Task *****');
      await this.execution();
    });
  }
}
