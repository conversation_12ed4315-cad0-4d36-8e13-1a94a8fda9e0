# Gmail 自动回复功能

## 功能概述

这个功能实现了Gmail邮箱的OAuth授权和自动邮件回复系统。当用户完成Gmail授权后，系统会自动：

1. 读取用户邮箱中的未读邮件
2. 基于项目知识库和AI生成智能回复
3. 自动发送回复邮件
4. 标记原邮件为已读

## 主要组件

### 1. EmailController (`src/controller/emailcontroller.ts`)
- **Gmail授权回调**: `/v1/auth/gmail/callback`
- **手动触发邮件处理**: `POST /v1/emails/:mailId/process`
- **检查授权状态**: `GET /v1/emails/checkAuthorized`

### 2. GmailService (`src/component/service/gMail.ts`)
- Gmail API集成
- OAuth认证管理
- 邮件读取、发送、标记功能

### 3. AIEmailReplyService (`src/component/service/aiEmailReply.ts`)
- 基于知识库的AI回复生成
- OpenAI备用方案
- 邮件格式化和签名

### 4. EmailProcessorService (`src/component/service/emailProcessor.ts`)
- 邮件处理流程协调
- 错误处理和重试机制
- 批量邮件处理

## 使用流程

### 方式一：预先添加邮箱配置（推荐）

#### 1. 添加邮箱配置
```http
POST /v1/emails
{
  "projectId": "your-project-id",
  "type": "Gmail",
  "sender": "Your Name",
  "email": "<EMAIL>",
  "companyName": "Your Company",
  "companyAddress": "Your Address",
  "productPage": "https://your-website.com",
  "contactUs": "<EMAIL>"
}
```

### 方式二：直接授权（自动创建记录）

如果您不想预先添加邮箱配置，可以直接进行授权，系统会自动创建邮箱记录：

### 2. 检查授权状态
```http
GET /v1/emails/checkAuthorized?email=<EMAIL>
```

如果未授权，会返回授权URL，用户需要访问该URL完成Gmail授权。

### 3. Gmail授权回调
用户完成授权后，Google会重定向到：
```
GET /v1/auth/gmail/callback?code=xxx&state=xxx
```

系统会自动：
- 获取访问令牌和刷新令牌
- 创建新邮箱记录或更新现有记录
- 关联项目和用户信息
- 自动处理未读邮件

### 4. 手动触发邮件处理
```http
POST /v1/emails/{mailId}/process
```

### 5. 更新邮箱记录关联（可选）
如果授权时没有提供完整信息，可以后续更新：
```http
PATCH /v1/emails/{mailId}/associate
{
  "projectId": "your-project-id",
  "companyName": "Your Company",
  "companyAddress": "Your Address",
  "productPage": "https://your-website.com",
  "contactUs": "<EMAIL>"
}
```

## 配置要求

### 1. Google OAuth配置
在 `src/resource/config.json` 中配置：
```json
{
  "SSO": {
    "google": {
      "client_id": "your-google-client-id",
      "client_secret": "your-google-client-secret",
      "gmail_redirect_uri": "https://your-domain.com/v1/auth/gmail/callback"
    }
  }
}
```

### 2. 数据库更新
确保Mail实体包含以下字段：
- `accessToken`: 访问令牌
- `refreshToken`: 刷新令牌
- `expiresAt`: 令牌过期时间
- `authorized`: 授权状态

### 3. Gmail API权限
需要以下权限范围：
- `https://www.googleapis.com/auth/gmail.readonly` - 读取邮件
- `https://www.googleapis.com/auth/gmail.send` - 发送邮件
- `https://www.googleapis.com/auth/gmail.modify` - 修改邮件状态

## AI回复策略

### 1. 知识库优先
系统首先尝试使用项目的知识库API生成回复，这样可以提供更准确、更相关的回复内容。

### 2. OpenAI备用
如果知识库API失败，系统会使用OpenAI作为备用方案，基于项目信息和自定义提示词生成回复。

### 3. 回复格式化
- 自动生成合适的回复主题（添加"Re:"前缀）
- 包含公司信息和联系方式的邮件签名
- 支持HTML和纯文本格式

## 错误处理

### 1. 重试机制
- 获取邮件失败：最多重试3次，递增延迟
- 处理单封邮件失败：最多重试3次
- AI生成失败：自动切换到备用方案

### 2. 日志记录
- 详细的处理日志，包含成功和失败信息
- 错误信息收集，便于调试和监控

### 3. 优雅降级
- 单封邮件处理失败不影响其他邮件
- 部分功能失败时提供有意义的错误信息

## 安全考虑

### 1. 令牌管理
- 访问令牌和刷新令牌安全存储
- 自动令牌刷新机制
- 令牌过期检查

### 2. 权限控制
- 只处理已授权和激活的邮箱
- 用户级别的权限验证
- 项目级别的隔离

### 3. 速率限制
- 限制每次处理的邮件数量（默认5-10封）
- 避免API配额超限
- 合理的重试间隔

## 监控和维护

### 1. 处理统计
每次处理会返回详细的统计信息：
```json
{
  "success": true,
  "processedCount": 5,
  "failedCount": 0,
  "errors": []
}
```

### 2. 日志监控
关键操作都有详细日志，便于监控和调试：
- `✅` 成功操作
- `⚠️` 警告信息
- `❌` 错误信息
- `🔍` 调试信息

### 3. 定期维护
建议定期检查：
- 令牌过期情况
- 处理失败率
- API配额使用情况

## 扩展性

### 1. 支持其他邮件服务
当前架构支持扩展到其他邮件服务（如Outlook），只需实现相应的服务类。

### 2. 自定义AI模型
可以轻松集成其他AI服务或自定义模型。

### 3. 高级过滤
可以添加邮件过滤规则，只处理特定类型的邮件。

## 故障排除

### 常见问题

1. **授权失败**
   - 检查Google OAuth配置
   - 确认回调URL正确
   - 验证权限范围

2. **邮件读取失败**
   - 检查访问令牌是否过期
   - 验证Gmail API配额
   - 确认网络连接

3. **AI回复生成失败**
   - 检查知识库API配置
   - 验证OpenAI API密钥
   - 查看错误日志

4. **邮件发送失败**
   - 检查发送权限
   - 验证收件人地址
   - 确认邮件格式

### 调试技巧

1. 启用详细日志记录
2. 使用手动触发API测试
3. 检查数据库中的令牌状态
4. 验证Gmail API配额使用情况
