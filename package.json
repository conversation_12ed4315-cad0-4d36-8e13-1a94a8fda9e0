{"name": "chat-assist-backend", "version": "1.0.0", "private": true, "repository": "http://gitlab.tslsmart.com:7964/tslapp/chat-assist-backend", "scripts": {"build": "rm -rf dist/ && tsc && cp -r src/resource dist", "start": "node dist/index.js", "dev": "npm run build && cross-env NODE_ENV=dev PORT=10070 npm start", "start:dev": "npm run build && cross-env NODE_ENV=dev PORT=10070 && nodemon dist/index.js", "migration:generate": "npm run build && typeorm migration:generate -d ormconfig.ts", "migration:create": "typeorm migration:create"}, "dependencies": {"@alicloud/dm20151123": "^1.6.1", "@alicloud/openapi-client": "^0.4.15", "@iarna/toml": "^2.2.5", "@mendable/firecrawl-js": "^1.29.1", "@types/node-cron": "^3.0.11", "ali-oss": "^6.20.0", "axios": "^1.6.8", "cheerio": "^1.0.0-rc.10", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "common-db-node": "github:110013873/common-db-node", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "dompurify": "^3.2.6", "express": "^4.18.2", "form-data": "^4.0.0", "google-auth-library": "^10.3.0", "googleapis": "^159.0.0", "http-proxy-middleware": "^2.0.6", "http-status-codes": "^2.3.0", "jsdom": "^26.1.0", "jsonwebtoken": "^8.5.1", "markdown-it": "^14.1.0", "node-cron": "^3.0.3", "openai": "^5.12.1", "openid-client": "^5.6.5", "os": "^0.1.2", "os-utils": "^0.0.14", "pg": "^8.11.3", "reflect-metadata": "^0.1.13", "routing-controllers": "^0.10.4", "stripe": "^18.3.0", "typeorm": "^0.3.17", "uuid": "^8.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/cheerio": "^1.0.0", "@types/cors": "^2.8.17", "@types/dompurify": "^3.2.0", "@types/express": "^4.17.21", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.17.4", "@types/markdown-it": "^14.1.2", "@types/multer": "^1.4.11", "@types/node": "^14.18.63", "@types/nunjucks": "^3.2.6", "@types/uuid": "^8.0.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "cross-env": "^7.0.3", "eslint": "^8.55.0", "nodemon": "^3.1.0", "patch-package": "^8.0.0", "typescript": "^4.9.5"}}