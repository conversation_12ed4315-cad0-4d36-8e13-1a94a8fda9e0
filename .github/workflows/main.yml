name: Build and Push Docker Image to <PERSON><PERSON>

on:
  push:
    branches:
      - main  # 可以改成你要触发的分支

jobs:
  docker-build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Aliyun Container Registry (ACR)
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.ALIYUN_REGISTRY }}
          username: ${{ secrets.ALIYUN_DOCKER_USERNAME }}
          password: ${{ secrets.ALIYUN_DOCKER_PASSWORD }}

      - name: Extract short SHA
        id: vars
        run: echo "SHORT_SHA=$(git rev-parse --short HEAD)" >> $GITHUB_ENV

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            ${{ secrets.ALIYUN_REGISTRY }}/chat-assist/chat-assist-backend:latest
            ${{ secrets.ALIYUN_REGISTRY }}/chat-assist/chat-assist-backend:${{ env.SHORT_SHA }}
