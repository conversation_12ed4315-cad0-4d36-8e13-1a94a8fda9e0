build_image:
  stage: build
  only:
    - main
    - develop
  before_script:
    - echo "$ALIYUN_DOCKER_PASSWORD" | docker login "$**********************" --username "$ALIYUN_DOCKER_USERNAME" --password-stdin
    - "[[ $CI_COMMIT_BRANCH == main ]] && NAMESPACE=optimism-prod || NAMESPACE=optimism-test"
  script: |
    docker build --rm \
      --build-arg GITLAB_TOKEN_NAME=$GITLAB_TOKEN_NAME \
      --build-arg GITLAB_TOKEN=$GITLAB_TOKEN \
    --add-host gitlab.tslsmart.com:************ \
      -t $**********************/$NAMESPACE/chat-assist-backend:$CI_COMMIT_SHORT_SHA \
      -t $**********************/$NAMESPACE/chat-assist-backend:latest .
    docker push $**********************/$NAMESPACE/chat-assist-backend:$CI_COMMIT_SHORT_SHA
    docker push $**********************/$NAMESPACE/chat-assist-backend:latest
