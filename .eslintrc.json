{"plugins": ["@typescript-eslint"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking"], "env": {"node": true}, "ignorePatterns": ["src/migration/*"], "rules": {"indent": ["error", 2, {"SwitchCase": 1, "ignoredNodes": ["PropertyDefinition"]}], "semi": ["error", "always"], "comma-dangle": ["error", "always-multiline"], "quotes": ["error", "single", {"allowTemplateLiterals": true}], "max-len": ["error", 999], "object-curly-spacing": ["error", "always"], "object-curly-newline": ["error", {"ImportDeclaration": {"multiline": false}}], "restrict-template-expressions": "off", "@typescript-eslint/restrict-template-expressions": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/require-await": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-call": "off"}}